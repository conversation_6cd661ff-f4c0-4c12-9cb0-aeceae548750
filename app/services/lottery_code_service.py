from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from datetime import datetime
import uuid
import random

from app.models.lottery_code import LotteryCode
from app.schemas.lottery_code import LotteryCodeCreate, LotteryCodeUpdate, LotteryCodeBatchCreate
from app.core.exceptions import NotFoundException, ValidationException
from app.utils.snowflake import generate_snowflake_id
from app.utils.logger import get_logger

logger = get_logger(__name__)


class LotteryCodeService:
    """抽奖二维码服务类"""
    
    @staticmethod
    def generate_code() -> str:
        """生成唯一编码（UUID去除-）"""
        return str(uuid.uuid4()).replace('-', '')
    
    @staticmethod
    async def create_lottery_code(db: AsyncSession, code_data: LotteryCodeCreate) -> LotteryCode:
        """创建抽奖二维码"""
        # 生成雪花ID和唯一编码
        snowflake_id = generate_snowflake_id()
        unique_code = LotteryCodeService.generate_code()
        
        # 检查编码是否已存在（理论上不会重复，但保险起见）
        existing_code = await LotteryCodeService.get_code_by_code(db, unique_code)
        while existing_code:
            unique_code = LotteryCodeService.generate_code()
            existing_code = await LotteryCodeService.get_code_by_code(db, unique_code)
        
        # 创建二维码记录
        lottery_code = LotteryCode(
            id=snowflake_id,
            code=unique_code,
            url=code_data.url,
            prize_level=code_data.prize_level,
            is_used=False
        )
        
        db.add(lottery_code)
        await db.commit()
        await db.refresh(lottery_code)
        
        logger.info(f"创建抽奖二维码成功: ID={lottery_code.id}, Code={lottery_code.code}")
        return lottery_code
    
    @staticmethod
    async def batch_create_lottery_codes(
        db: AsyncSession, 
        batch_data: LotteryCodeBatchCreate
    ) -> List[LotteryCode]:
        """批量创建抽奖二维码"""
        codes = []
        
        for i in range(batch_data.count):
            # 生成雪花ID和唯一编码
            snowflake_id = generate_snowflake_id()
            unique_code = LotteryCodeService.generate_code()
            
            # 确保编码唯一性
            existing_code = await LotteryCodeService.get_code_by_code(db, unique_code)
            while existing_code:
                unique_code = LotteryCodeService.generate_code()
                existing_code = await LotteryCodeService.get_code_by_code(db, unique_code)
            
            # 生成URL
            url = batch_data.url_template.format(code=unique_code)
            
            # 随机分配奖品等级
            prize_level = 0
            if batch_data.prize_levels:
                prize_level = random.choice(batch_data.prize_levels)
            
            # 创建二维码记录
            lottery_code = LotteryCode(
                id=snowflake_id,
                code=unique_code,
                url=url,
                prize_level=prize_level,
                is_used=False
            )
            
            codes.append(lottery_code)
        
        # 批量插入
        db.add_all(codes)
        await db.commit()
        
        logger.info(f"批量创建抽奖二维码成功: 数量={len(codes)}")
        return codes
    
    @staticmethod
    async def get_code_by_id(db: AsyncSession, code_id: int) -> Optional[LotteryCode]:
        """根据ID获取抽奖二维码"""
        result = await db.execute(select(LotteryCode).where(LotteryCode.id == code_id))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_code_by_code(db: AsyncSession, code: str) -> Optional[LotteryCode]:
        """根据编码获取抽奖二维码"""
        result = await db.execute(select(LotteryCode).where(LotteryCode.code == code))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_lottery_code(
        db: AsyncSession, 
        code_id: int, 
        code_data: LotteryCodeUpdate
    ) -> LotteryCode:
        """更新抽奖二维码"""
        lottery_code = await LotteryCodeService.get_code_by_id(db, code_id)
        if not lottery_code:
            raise NotFoundException(f"抽奖二维码ID {code_id} 不存在")
        
        # 更新字段
        update_data = code_data.dict(exclude_unset=True)
        
        for key, value in update_data.items():
            setattr(lottery_code, key, value)
        
        await db.commit()
        await db.refresh(lottery_code)
        
        logger.info(f"更新抽奖二维码成功: ID={lottery_code.id}")
        return lottery_code
    
    @staticmethod
    async def delete_lottery_code(db: AsyncSession, code_id: int) -> bool:
        """删除抽奖二维码"""
        lottery_code = await LotteryCodeService.get_code_by_id(db, code_id)
        if not lottery_code:
            raise NotFoundException(f"抽奖二维码ID {code_id} 不存在")
        
        await db.delete(lottery_code)
        await db.commit()
        
        logger.info(f"删除抽奖二维码成功: ID={code_id}")
        return True
    
    @staticmethod
    async def get_lottery_codes(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 10,
        is_used: Optional[bool] = None,
        prize_level: Optional[int] = None
    ) -> Tuple[List[LotteryCode], int]:
        """获取抽奖二维码列表"""
        query = select(LotteryCode)
        
        # 添加过滤条件
        conditions = []
        if is_used is not None:
            conditions.append(LotteryCode.is_used == is_used)
        if prize_level is not None:
            conditions.append(LotteryCode.prize_level == prize_level)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count(LotteryCode.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 获取分页数据
        query = query.offset(skip).limit(limit).order_by(LotteryCode.created_at.desc())
        result = await db.execute(query)
        codes = result.scalars().all()
        
        return list(codes), total

    @staticmethod
    async def scan_lottery_code(
        db: AsyncSession,
        code: str,
        ip_address: str
    ) -> dict:
        """扫码抽奖"""
        # 获取二维码记录
        lottery_code = await LotteryCodeService.get_code_by_code(db, code)
        if not lottery_code:
            return {
                "success": False,
                "message": "二维码不存在",
                "prize_level": None,
                "prize_name": None,
                "code": code
            }

        # 检查是否已使用
        if lottery_code.is_used:
            return {
                "success": False,
                "message": "二维码已使用过",
                "prize_level": lottery_code.prize_level,
                "prize_name": LotteryCodeService.get_prize_name(lottery_code.prize_level),
                "code": code
            }

        # 更新使用状态
        lottery_code.is_used = True
        lottery_code.ip = ip_address
        lottery_code.scan_time = datetime.now()

        await db.commit()
        await db.refresh(lottery_code)

        # 返回中奖结果
        prize_name = LotteryCodeService.get_prize_name(lottery_code.prize_level)
        message = "恭喜中奖！" if lottery_code.prize_level > 0 else "谢谢参与！"

        logger.info(f"扫码成功: Code={code}, IP={ip_address}, Prize={lottery_code.prize_level}")

        return {
            "success": True,
            "message": message,
            "prize_level": lottery_code.prize_level,
            "prize_name": prize_name,
            "code": code
        }

    @staticmethod
    def get_prize_name(prize_level: int) -> str:
        """根据奖品等级获取奖品名称"""
        prize_names = {
            0: "谢谢参与",
            1: "一等奖",
            2: "二等奖",
            3: "三等奖",
            4: "四等奖",
            5: "五等奖",
            6: "六等奖",
            7: "七等奖",
            8: "八等奖",
            9: "九等奖",
            10: "特等奖"
        }
        return prize_names.get(prize_level, f"第{prize_level}等奖")

    @staticmethod
    async def get_lottery_stats(db: AsyncSession) -> dict:
        """获取抽奖统计信息"""
        # 总数统计
        total_result = await db.execute(select(func.count(LotteryCode.id)))
        total_codes = total_result.scalar()

        # 已使用数统计
        used_result = await db.execute(
            select(func.count(LotteryCode.id)).where(LotteryCode.is_used == True)
        )
        used_codes = used_result.scalar()

        # 未使用数
        unused_codes = total_codes - used_codes

        # 奖品等级统计
        prize_result = await db.execute(
            select(LotteryCode.prize_level, func.count(LotteryCode.id))
            .group_by(LotteryCode.prize_level)
        )
        prize_stats = {str(level): count for level, count in prize_result.fetchall()}

        # 扫码统计（按日期）
        scan_result = await db.execute(
            select(
                func.date(LotteryCode.scan_time).label('scan_date'),
                func.count(LotteryCode.id).label('scan_count')
            )
            .where(LotteryCode.scan_time.isnot(None))
            .group_by(func.date(LotteryCode.scan_time))
            .order_by(func.date(LotteryCode.scan_time).desc())
            .limit(30)  # 最近30天
        )
        scan_stats = {str(date): count for date, count in scan_result.fetchall()}

        return {
            "total_codes": total_codes,
            "used_codes": used_codes,
            "unused_codes": unused_codes,
            "prize_stats": prize_stats,
            "scan_stats": scan_stats
        }
