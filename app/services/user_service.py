from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.exceptions import NotFoundException, ValidationException
from app.core.security import get_password_hash, verify_password
from app.utils.logger import get_logger

logger = get_logger(__name__)


class UserService:
    """用户服务类"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        return get_password_hash(password)

    @staticmethod
    def verify_password_hash(password: str, password_hash: str) -> bool:
        """验证密码"""
        return verify_password(password, password_hash)
    
    @staticmethod
    async def create_user(db: AsyncSession, user_data: UserCreate) -> User:
        """创建用户"""
        # 检查用户名是否已存在
        existing_user = await UserService.get_user_by_username(db, user_data.username)
        if existing_user:
            raise ValidationException(f"用户名 '{user_data.username}' 已存在")
        
        # 检查邮箱是否已存在
        existing_email = await UserService.get_user_by_email(db, user_data.email)
        if existing_email:
            raise ValidationException(f"邮箱 '{user_data.email}' 已存在")
        
        # 创建用户
        user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=UserService.hash_password(user_data.password),
            full_name=user_data.full_name,
            phone=user_data.phone,
            avatar_url=user_data.avatar_url,
            bio=user_data.bio,
            is_active=user_data.is_active
        )
        
        db.add(user)
        await db.commit()
        await db.refresh(user)
        
        logger.info(f"创建用户成功: {user.username}")
        return user
    
    @staticmethod
    async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        result = await db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_user(db: AsyncSession, user_id: int, user_data: UserUpdate) -> User:
        """更新用户"""
        user = await UserService.get_user_by_id(db, user_id)
        if not user:
            raise NotFoundException(f"用户ID {user_id} 不存在")
        
        # 更新字段
        update_data = user_data.dict(exclude_unset=True)
        
        # 处理密码更新
        if "password" in update_data:
            update_data["password_hash"] = UserService.hash_password(update_data.pop("password"))
        
        # 检查用户名和邮箱唯一性
        if "username" in update_data and update_data["username"] != user.username:
            existing_user = await UserService.get_user_by_username(db, update_data["username"])
            if existing_user:
                raise ValidationException(f"用户名 '{update_data['username']}' 已存在")
        
        if "email" in update_data and update_data["email"] != user.email:
            existing_email = await UserService.get_user_by_email(db, update_data["email"])
            if existing_email:
                raise ValidationException(f"邮箱 '{update_data['email']}' 已存在")
        
        # 更新用户
        user.update_from_dict(update_data)
        await db.commit()
        await db.refresh(user)
        
        logger.info(f"更新用户成功: {user.username}")
        return user
    
    @staticmethod
    async def delete_user(db: AsyncSession, user_id: int) -> bool:
        """删除用户"""
        user = await UserService.get_user_by_id(db, user_id)
        if not user:
            raise NotFoundException(f"用户ID {user_id} 不存在")
        
        await db.delete(user)
        await db.commit()
        
        logger.info(f"删除用户成功: {user.username}")
        return True

    @staticmethod
    async def get_users(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 10,
        is_active: Optional[bool] = None
    ) -> tuple[List[User], int]:
        """获取用户列表"""
        query = select(User)

        # 添加过滤条件
        if is_active is not None:
            query = query.where(User.is_active == is_active)

        # 获取总数
        count_query = select(func.count(User.id))
        if is_active is not None:
            count_query = count_query.where(User.is_active == is_active)

        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 获取分页数据
        query = query.offset(skip).limit(limit).order_by(User.created_at.desc())
        result = await db.execute(query)
        users = result.scalars().all()

        return list(users), total

    @staticmethod
    async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
        """用户认证"""
        # 尝试用户名登录
        user = await UserService.get_user_by_username(db, username)

        # 如果用户名不存在，尝试邮箱登录
        if not user:
            user = await UserService.get_user_by_email(db, username)

        if not user or not user.is_active:
            return None

        if not UserService.verify_password_hash(password, user.password_hash):
            return None

        logger.info(f"用户认证成功: {user.username}")
        return user
