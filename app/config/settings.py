import os
import yaml
from typing import Any, Dict, List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = ""
    database: str = "activity_lottery"
    charset: str = "utf8mb4"
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600

    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return f"mysql+aiomysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


class AppSettings(BaseSettings):
    """应用配置"""
    name: str = "Activity Lottery API"
    version: str = "1.0.0"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/app.log"
    max_bytes: int = 10485760  # 10MB
    backup_count: int = 5
    console_output: bool = True


class CORSSettings(BaseSettings):
    """CORS配置"""
    allow_origins: List[str] = ["*"]
    allow_credentials: bool = True
    allow_methods: List[str] = ["*"]
    allow_headers: List[str] = ["*"]


class SecuritySettings(BaseSettings):
    """安全配置"""
    secret_key: str = "your-secret-key"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30


class Settings(BaseSettings):
    """主配置类"""
    environment: str = Field(default="dev", env="ENVIRONMENT")
    
    app: AppSettings = AppSettings()
    database: DatabaseSettings = DatabaseSettings()
    logging: LoggingSettings = LoggingSettings()
    cors: CORSSettings = CORSSettings()
    security: SecuritySettings = SecuritySettings()

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._load_config()

    def _load_config(self):
        """从YAML文件加载配置"""
        config_file = f"configs/{self.environment}.yaml"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
                
            # 解析环境变量
            config_data = self._resolve_env_vars(config_data)
            
            # 更新配置
            if 'app' in config_data:
                self.app = AppSettings(**config_data['app'])
            if 'database' in config_data:
                self.database = DatabaseSettings(**config_data['database'])
            if 'logging' in config_data:
                self.logging = LoggingSettings(**config_data['logging'])
            if 'cors' in config_data:
                self.cors = CORSSettings(**config_data['cors'])
            if 'security' in config_data:
                self.security = SecuritySettings(**config_data['security'])

    def _resolve_env_vars(self, data: Any) -> Any:
        """解析环境变量占位符"""
        if isinstance(data, dict):
            return {k: self._resolve_env_vars(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._resolve_env_vars(item) for item in data]
        elif isinstance(data, str) and data.startswith("${") and data.endswith("}"):
            # 解析 ${VAR_NAME:default_value} 格式
            var_expr = data[2:-1]  # 移除 ${ 和 }
            if ":" in var_expr:
                var_name, default_value = var_expr.split(":", 1)
                return os.getenv(var_name, default_value)
            else:
                return os.getenv(var_expr, "")
        return data

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
