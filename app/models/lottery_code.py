from sqlalchemy import Column, BigInteger, Integer, String, DateTime, Boolean
from sqlalchemy.sql import func
from .base import BaseModel


class LotteryCode(BaseModel):
    """抽奖二维码模型"""
    __tablename__ = "lottery_code"
    
    # 重写id字段为BigInteger（雪花ID）
    id = Column(BigInteger, primary_key=True, comment="雪花ID")
    code = Column(String(32), unique=True, index=True, nullable=False, comment="唯一编码(UUID去除-)")
    url = Column(String(255), nullable=False, comment="二维码链接")
    prize_level = Column(Integer, default=0, nullable=False, comment="中奖等级")
    is_used = Column(Boolean, default=False, nullable=False, comment="是否使用过")
    ip = Column(String(32), nullable=True, comment="IP地址")
    scan_time = Column(DateTime, nullable=True, comment="扫码时间")
    
    # 重写时间字段名以匹配数据库
    create_at = Column(DateTime, default=func.now(), nullable=True, comment="创建时间")
    update_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=True, comment="更新时间")
    
    def __repr__(self):
        return f"<LotteryCode(id={self.id}, code='{self.code}', prize_level={self.prize_level})>"
    
    def to_dict(self, exclude: set = None) -> dict:
        """转换为字典，重写以处理特殊字段"""
        exclude = exclude or set()
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude:
                value = getattr(self, column.name)
                if hasattr(value, 'isoformat'):  # datetime对象
                    value = value.isoformat()
                result[column.name] = value
        
        return result
