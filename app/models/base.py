from datetime import datetime
from typing import Any, Dict
from sqlalchemy import <PERSON><PERSON>n, Integer, DateTime, func
from sqlalchemy.ext.declarative import declared_attr
from app.config.database import Base


class TimestampMixin:
    """时间戳混入类"""
    
    @declared_attr
    def created_at(cls):
        return Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    
    @declared_attr
    def updated_at(cls):
        return Column(
            DateTime, 
            default=func.now(), 
            onupdate=func.now(), 
            nullable=False, 
            comment="更新时间"
        )


class BaseModel(Base, TimestampMixin):
    """数据库模型基类"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True, comment="主键ID")
    
    def to_dict(self, exclude: set = None) -> Dict[str, Any]:
        """转换为字典"""
        exclude = exclude or set()
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude: set = None):
        """从字典更新属性"""
        exclude = exclude or {"id", "created_at", "updated_at"}
        
        for key, value in data.items():
            if key not in exclude and hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"
