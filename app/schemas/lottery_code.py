from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime
import re


class LotteryCodeBase(BaseModel):
    """抽奖二维码基础模型"""
    url: str = Field(..., max_length=255, description="二维码链接")
    prize_level: int = Field(default=0, ge=0, description="中奖等级")
    
    @validator('url')
    def validate_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('URL必须以http://或https://开头')
        return v


class LotteryCodeCreate(LotteryCodeBase):
    """创建抽奖二维码模型"""
    pass


class LotteryCodeUpdate(BaseModel):
    """更新抽奖二维码模型"""
    url: Optional[str] = Field(None, max_length=255, description="二维码链接")
    prize_level: Optional[int] = Field(None, ge=0, description="中奖等级")
    
    @validator('url')
    def validate_url(cls, v):
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('URL必须以http://或https://开头')
        return v


class LotteryCodeResponse(LotteryCodeBase):
    """抽奖二维码响应模型"""
    id: int = Field(..., description="雪花ID")
    code: str = Field(..., description="唯一编码")
    is_used: bool = Field(..., description="是否使用过")
    ip: Optional[str] = Field(None, description="IP地址")
    scan_time: Optional[datetime] = Field(None, description="扫码时间")
    create_at: Optional[datetime] = Field(None, description="创建时间")
    update_at: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class LotteryCodeList(BaseModel):
    """抽奖二维码列表响应模型"""
    codes: List[LotteryCodeResponse] = Field(..., description="二维码列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class LotteryCodeScan(BaseModel):
    """扫码请求模型"""
    code: str = Field(..., min_length=32, max_length=32, description="二维码编码")
    
    @validator('code')
    def validate_code(cls, v):
        # 验证是否为32位字符串（UUID去除-后的格式）
        if not re.match(r'^[a-f0-9]{32}$', v.lower()):
            raise ValueError('编码格式不正确，应为32位十六进制字符串')
        return v.lower()


class LotteryCodeScanResponse(BaseModel):
    """扫码响应模型"""
    success: bool = Field(..., description="扫码是否成功")
    message: str = Field(..., description="响应消息")
    prize_level: Optional[int] = Field(None, description="中奖等级")
    prize_name: Optional[str] = Field(None, description="奖品名称")
    code: Optional[str] = Field(None, description="二维码编码")


class LotteryCodeBatchCreate(BaseModel):
    """批量创建二维码模型"""
    count: int = Field(..., ge=1, le=1000, description="创建数量")
    url_template: str = Field(..., description="URL模板，使用{code}作为占位符")
    prize_levels: Optional[List[int]] = Field(None, description="奖品等级列表，随机分配")
    
    @validator('url_template')
    def validate_url_template(cls, v):
        if '{code}' not in v:
            raise ValueError('URL模板必须包含{code}占位符')
        if not v.replace('{code}', 'test').startswith(('http://', 'https://')):
            raise ValueError('URL模板必须以http://或https://开头')
        return v


class LotteryCodeStats(BaseModel):
    """抽奖二维码统计模型"""
    total_codes: int = Field(..., description="总二维码数")
    used_codes: int = Field(..., description="已使用数")
    unused_codes: int = Field(..., description="未使用数")
    prize_stats: dict = Field(..., description="奖品等级统计")
    scan_stats: dict = Field(..., description="扫码统计")
