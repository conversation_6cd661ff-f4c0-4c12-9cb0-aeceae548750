from typing import Any, Optional
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging

logger = logging.getLogger(__name__)


class APIException(Exception):
    """自定义API异常基类"""
    
    def __init__(
        self,
        message: str = "API错误",
        code: int = 400,
        data: Any = None
    ):
        self.message = message
        self.code = code
        self.data = data
        super().__init__(self.message)


class ValidationException(APIException):
    """验证异常"""
    
    def __init__(self, message: str = "数据验证失败", data: Any = None):
        super().__init__(message=message, code=422, data=data)


class DatabaseException(APIException):
    """数据库异常"""
    
    def __init__(self, message: str = "数据库操作失败", data: Any = None):
        super().__init__(message=message, code=500, data=data)


class AuthenticationException(APIException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(message=message, code=401)


class AuthorizationException(APIException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足"):
        super().__init__(message=message, code=403)


class NotFoundException(APIException):
    """资源不存在异常"""
    
    def __init__(self, message: str = "资源不存在"):
        super().__init__(message=message, code=404)


async def api_exception_handler(request: Request, exc: APIException) -> JSONResponse:
    """API异常处理器"""
    from .response import error_response
    
    logger.error(f"API异常: {exc.message}, 路径: {request.url.path}")
    
    response = error_response(
        message=exc.message,
        code=exc.code,
        data=exc.data
    )
    
    return JSONResponse(
        status_code=exc.code,
        content=response.dict()
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    from .response import error_response
    
    logger.error(f"HTTP异常: {exc.detail}, 状态码: {exc.status_code}, 路径: {request.url.path}")
    
    response = error_response(
        message=str(exc.detail),
        code=exc.status_code
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=response.dict()
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """请求验证异常处理器"""
    from .response import error_response
    
    logger.error(f"验证异常: {exc.errors()}, 路径: {request.url.path}")
    
    # 格式化验证错误信息
    errors = []
    for error in exc.errors():
        field = " -> ".join(str(loc) for loc in error["loc"])
        errors.append(f"{field}: {error['msg']}")
    
    response = error_response(
        message="请求参数验证失败",
        code=422,
        data={"errors": errors}
    )
    
    return JSONResponse(
        status_code=422,
        content=response.dict()
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    from .response import error_response
    
    logger.error(f"未处理异常: {str(exc)}, 类型: {type(exc).__name__}, 路径: {request.url.path}", exc_info=True)
    
    response = error_response(
        message="服务器内部错误",
        code=500
    )
    
    return JSONResponse(
        status_code=500,
        content=response.dict()
    )
