from typing import Any, Optional, Generic, TypeVar
from pydantic import BaseModel
from fastapi import status

T = TypeVar('T')


class ResponseModel(BaseModel, Generic[T]):
    """统一响应模型"""
    code: int = 200
    message: str = "success"
    data: Optional[T] = None
    success: bool = True
    timestamp: Optional[str] = None

    class Config:
        json_encoders = {
            # 可以在这里添加自定义的JSON编码器
        }


def success_response(
    data: Any = None,
    message: str = "操作成功",
    code: int = status.HTTP_200_OK
) -> ResponseModel:
    """成功响应"""
    from datetime import datetime
    return ResponseModel(
        code=code,
        message=message,
        data=data,
        success=True,
        timestamp=datetime.now().isoformat()
    )


def error_response(
    message: str = "操作失败",
    code: int = status.HTTP_400_BAD_REQUEST,
    data: Any = None
) -> ResponseModel:
    """错误响应"""
    from datetime import datetime
    return ResponseModel(
        code=code,
        message=message,
        data=data,
        success=False,
        timestamp=datetime.now().isoformat()
    )


# 常用响应模板
class CommonResponse:
    """常用响应模板"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功"):
        return success_response(data=data, message=message)
    
    @staticmethod
    def created(data: Any = None, message: str = "创建成功"):
        return success_response(data=data, message=message, code=status.HTTP_201_CREATED)
    
    @staticmethod
    def updated(data: Any = None, message: str = "更新成功"):
        return success_response(data=data, message=message)
    
    @staticmethod
    def deleted(message: str = "删除成功"):
        return success_response(message=message)
    
    @staticmethod
    def not_found(message: str = "资源不存在"):
        return error_response(message=message, code=status.HTTP_404_NOT_FOUND)
    
    @staticmethod
    def bad_request(message: str = "请求参数错误"):
        return error_response(message=message, code=status.HTTP_400_BAD_REQUEST)
    
    @staticmethod
    def unauthorized(message: str = "未授权访问"):
        return error_response(message=message, code=status.HTTP_401_UNAUTHORIZED)
    
    @staticmethod
    def forbidden(message: str = "禁止访问"):
        return error_response(message=message, code=status.HTTP_403_FORBIDDEN)
    
    @staticmethod
    def internal_error(message: str = "服务器内部错误"):
        return error_response(message=message, code=status.HTTP_500_INTERNAL_SERVER_ERROR)
