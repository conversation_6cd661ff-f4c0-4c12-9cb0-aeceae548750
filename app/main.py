from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.config.settings import settings
from app.config.logging import setup_logging
from app.config.database import init_db, close_db
from app.core.exceptions import (
    APIException,
    api_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.core.middleware import LoggingMiddleware, SecurityHeadersMiddleware
from app.api import api_router
import logging


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info(f"正在启动 {settings.app.name} v{settings.app.version}")
    logger.info(f"运行环境: {settings.environment}")
    
    try:
        # 初始化数据库
        await init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.warning(f"数据库初始化失败: {str(e)}")
        logger.warning("应用将在无数据库模式下启动，部分功能可能不可用")
    
    logger.info("应用启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("正在关闭应用...")
    try:
        await close_db()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {str(e)}")
    
    logger.info("应用已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    app = FastAPI(
        title=settings.app.name,
        version=settings.app.version,
        description="Activity Lottery API - 活动抽奖系统",
        debug=settings.app.debug,
        lifespan=lifespan,
        docs_url="/docs" if settings.app.debug else None,
        redoc_url="/redoc" if settings.app.debug else None,
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors.allow_origins,
        allow_credentials=settings.cors.allow_credentials,
        allow_methods=settings.cors.allow_methods,
        allow_headers=settings.cors.allow_headers,
    )
    
    # 添加自定义中间件
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
    
    # 注册异常处理器
    app.add_exception_handler(APIException, api_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 根路径
    @app.get("/")
    async def root():
        from app.core.response import success_response
        return success_response(
            data={
                "service": settings.app.name,
                "version": settings.app.version,
                "environment": settings.environment,
                "docs": "/docs" if settings.app.debug else "disabled",
            },
            message="欢迎使用活动抽奖系统API"
        )
    
    return app


# 创建应用实例
app = create_app()
