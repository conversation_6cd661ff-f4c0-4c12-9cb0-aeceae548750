from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.api.deps import get_database
from app.core.response import success_response, error_response
from app.config.settings import settings
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """基础健康检查"""
    return success_response(
        data={
            "status": "healthy",
            "service": settings.app.name,
            "version": settings.app.version,
            "environment": settings.environment
        },
        message="服务运行正常"
    )


@router.get("/detailed")
async def detailed_health_check():
    """详细健康检查，包括数据库连接"""
    health_data = {
        "status": "healthy",
        "service": settings.app.name,
        "version": settings.app.version,
        "environment": settings.environment,
        "database": "disconnected"
    }

    try:
        # 尝试获取数据库连接并测试
        async for db in get_database():
            result = await db.execute(text("SELECT 1"))
            if result:
                health_data["database"] = "connected"
                logger.info("数据库连接正常")
            break

        return success_response(
            data=health_data,
            message="服务运行正常，所有组件健康"
        )

    except Exception as e:
        logger.warning(f"数据库连接检查失败: {str(e)}")
        health_data["database"] = "disconnected"
        health_data["database_error"] = str(e)

        return success_response(
            data=health_data,
            message="服务运行正常，但数据库不可用"
        )


@router.get("/ping")
async def ping():
    """简单的ping接口"""
    return success_response(data="pong", message="服务响应正常")
