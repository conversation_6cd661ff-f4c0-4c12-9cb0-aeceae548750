from typing import Optional
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
import math

from app.api.deps import get_database
from app.core.response import success_response, CommonResponse
from app.core.exceptions import APIException
from app.schemas.user import UserC<PERSON>, UserUpdate, UserResponse, UserList
from app.services.user_service import UserService
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=dict, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_database)
):
    """创建新用户"""
    try:
        user = await UserService.create_user(db, user_data)
        user_response = UserResponse.from_orm(user)
        return CommonResponse.created(data=user_response.dict(), message="用户创建成功")
    except Exception as e:
        logger.error(f"创建用户失败: {str(e)}")
        raise APIException(f"创建用户失败: {str(e)}")


@router.get("/{user_id}", response_model=dict, summary="获取用户详情")
async def get_user(
    user_id: int = Path(..., description="用户ID"),
    db: AsyncSession = Depends(get_database)
):
    """根据ID获取用户详情"""
    try:
        user = await UserService.get_user_by_id(db, user_id)
        if not user:
            return CommonResponse.not_found("用户不存在")
        
        user_response = UserResponse.from_orm(user)
        return CommonResponse.success(data=user_response.dict(), message="获取用户成功")
    except Exception as e:
        logger.error(f"获取用户失败: {str(e)}")
        raise APIException(f"获取用户失败: {str(e)}")


@router.put("/{user_id}", response_model=dict, summary="更新用户")
async def update_user(
    user_data: UserUpdate,
    user_id: int = Path(..., description="用户ID"),
    db: AsyncSession = Depends(get_database)
):
    """更新用户信息"""
    try:
        user = await UserService.update_user(db, user_id, user_data)
        user_response = UserResponse.from_orm(user)
        return CommonResponse.updated(data=user_response.dict(), message="用户更新成功")
    except Exception as e:
        logger.error(f"更新用户失败: {str(e)}")
        raise APIException(f"更新用户失败: {str(e)}")


@router.delete("/{user_id}", response_model=dict, summary="删除用户")
async def delete_user(
    user_id: int = Path(..., description="用户ID"),
    db: AsyncSession = Depends(get_database)
):
    """删除用户"""
    try:
        await UserService.delete_user(db, user_id)
        return CommonResponse.deleted("用户删除成功")
    except Exception as e:
        logger.error(f"删除用户失败: {str(e)}")
        raise APIException(f"删除用户失败: {str(e)}")


@router.get("/", response_model=dict, summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    db: AsyncSession = Depends(get_database)
):
    """获取用户列表（分页）"""
    try:
        skip = (page - 1) * size
        users, total = await UserService.get_users(db, skip=skip, limit=size, is_active=is_active)
        
        # 转换为响应模型
        user_responses = [UserResponse.from_orm(user).dict() for user in users]
        
        # 计算总页数
        pages = math.ceil(total / size) if total > 0 else 1
        
        user_list = UserList(
            users=user_responses,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
        return CommonResponse.success(data=user_list.dict(), message="获取用户列表成功")
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        raise APIException(f"获取用户列表失败: {str(e)}")


@router.get("/username/{username}", response_model=dict, summary="根据用户名获取用户")
async def get_user_by_username(
    username: str = Path(..., description="用户名"),
    db: AsyncSession = Depends(get_database)
):
    """根据用户名获取用户"""
    try:
        user = await UserService.get_user_by_username(db, username)
        if not user:
            return CommonResponse.not_found("用户不存在")
        
        user_response = UserResponse.from_orm(user)
        return CommonResponse.success(data=user_response.dict(), message="获取用户成功")
    except Exception as e:
        logger.error(f"获取用户失败: {str(e)}")
        raise APIException(f"获取用户失败: {str(e)}")
