from datetime import <PERSON><PERSON><PERSON>
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_database
from app.core.response import CommonResponse
from app.core.security import create_access_token, verify_token
from app.core.exceptions import APIException
from app.schemas.user import User<PERSON>ogin, Token, UserResponse
from app.services.user_service import UserService
from app.config.settings import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_database)
):
    """获取当前用户"""
    try:
        payload = verify_token(token)
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = await UserService.get_user_by_id(db, user_id=user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


@router.post("/login", response_model=dict, summary="用户登录")
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_database)
):
    """用户登录获取访问令牌"""
    try:
        user = await UserService.authenticate_user(db, form_data.username, form_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        access_token_expires = timedelta(minutes=settings.security.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id), "username": user.username},
            expires_delta=access_token_expires
        )
        
        token_data = Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.security.access_token_expire_minutes * 60
        )
        
        return CommonResponse.success(
            data=token_data.dict(),
            message="登录成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        raise APIException(f"登录失败: {str(e)}")


@router.post("/login/json", response_model=dict, summary="JSON格式登录")
async def login_json(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_database)
):
    """JSON格式用户登录"""
    try:
        user = await UserService.authenticate_user(db, login_data.username, login_data.password)
        if not user:
            return CommonResponse.unauthorized("用户名或密码错误")
        
        access_token_expires = timedelta(minutes=settings.security.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id), "username": user.username},
            expires_delta=access_token_expires
        )
        
        token_data = Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.security.access_token_expire_minutes * 60
        )
        
        return CommonResponse.success(
            data=token_data.dict(),
            message="登录成功"
        )
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        raise APIException(f"登录失败: {str(e)}")


@router.get("/me", response_model=dict, summary="获取当前用户信息")
async def get_current_user_info(
    current_user = Depends(get_current_user)
):
    """获取当前登录用户的信息"""
    try:
        user_response = UserResponse.from_orm(current_user)
        return CommonResponse.success(
            data=user_response.dict(),
            message="获取用户信息成功"
        )
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        raise APIException(f"获取用户信息失败: {str(e)}")


@router.post("/logout", response_model=dict, summary="用户登出")
async def logout(current_user = Depends(get_current_user)):
    """用户登出（客户端需要删除令牌）"""
    # 在实际应用中，可以将令牌加入黑名单
    # 这里只是返回成功消息，客户端需要删除本地存储的令牌
    logger.info(f"用户登出: {current_user.username}")
    return CommonResponse.success(message="登出成功")
