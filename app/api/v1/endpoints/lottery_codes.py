from typing import Optional
from fastapi import APIRouter, Depends, Query, Path, Request
from sqlalchemy.ext.asyncio import AsyncSession
import math

from app.api.deps import get_database
from app.core.response import CommonResponse
from app.core.exceptions import APIException
from app.schemas.lottery_code import (
    LotteryCodeCreate, LotteryCodeUpdate, LotteryCodeResponse, 
    LotteryCodeList, LotteryCodeScan, LotteryCodeScanResponse,
    LotteryCodeBatchCreate, LotteryCodeStats
)
from app.services.lottery_code_service import LotteryCodeService
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    # 优先从X-Forwarded-For头获取（考虑代理情况）
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    # 从X-Real-IP头获取
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # 最后从客户端地址获取
    if request.client:
        return request.client.host
    
    return "unknown"


@router.post("/", response_model=dict, summary="创建抽奖二维码")
async def create_lottery_code(
    code_data: LotteryCodeCreate,
    db: AsyncSession = Depends(get_database)
):
    """创建单个抽奖二维码"""
    try:
        lottery_code = await LotteryCodeService.create_lottery_code(db, code_data)
        code_response = LotteryCodeResponse.from_orm(lottery_code)
        return CommonResponse.created(data=code_response.dict(), message="抽奖二维码创建成功")
    except Exception as e:
        logger.error(f"创建抽奖二维码失败: {str(e)}")
        raise APIException(f"创建抽奖二维码失败: {str(e)}")


@router.post("/batch", response_model=dict, summary="批量创建抽奖二维码")
async def batch_create_lottery_codes(
    batch_data: LotteryCodeBatchCreate,
    db: AsyncSession = Depends(get_database)
):
    """批量创建抽奖二维码"""
    try:
        lottery_codes = await LotteryCodeService.batch_create_lottery_codes(db, batch_data)
        code_responses = [LotteryCodeResponse.from_orm(code).dict() for code in lottery_codes]
        return CommonResponse.created(
            data={
                "codes": code_responses,
                "count": len(code_responses)
            }, 
            message=f"批量创建抽奖二维码成功，共创建{len(code_responses)}个"
        )
    except Exception as e:
        logger.error(f"批量创建抽奖二维码失败: {str(e)}")
        raise APIException(f"批量创建抽奖二维码失败: {str(e)}")


@router.get("/{code_id}", response_model=dict, summary="获取抽奖二维码详情")
async def get_lottery_code(
    code_id: int = Path(..., description="二维码ID"),
    db: AsyncSession = Depends(get_database)
):
    """根据ID获取抽奖二维码详情"""
    try:
        lottery_code = await LotteryCodeService.get_code_by_id(db, code_id)
        if not lottery_code:
            return CommonResponse.not_found("抽奖二维码不存在")
        
        code_response = LotteryCodeResponse.from_orm(lottery_code)
        return CommonResponse.success(data=code_response.dict(), message="获取抽奖二维码成功")
    except Exception as e:
        logger.error(f"获取抽奖二维码失败: {str(e)}")
        raise APIException(f"获取抽奖二维码失败: {str(e)}")


@router.put("/{code_id}", response_model=dict, summary="更新抽奖二维码")
async def update_lottery_code(
    code_data: LotteryCodeUpdate,
    code_id: int = Path(..., description="二维码ID"),
    db: AsyncSession = Depends(get_database)
):
    """更新抽奖二维码信息"""
    try:
        lottery_code = await LotteryCodeService.update_lottery_code(db, code_id, code_data)
        code_response = LotteryCodeResponse.from_orm(lottery_code)
        return CommonResponse.updated(data=code_response.dict(), message="抽奖二维码更新成功")
    except Exception as e:
        logger.error(f"更新抽奖二维码失败: {str(e)}")
        raise APIException(f"更新抽奖二维码失败: {str(e)}")


@router.delete("/{code_id}", response_model=dict, summary="删除抽奖二维码")
async def delete_lottery_code(
    code_id: int = Path(..., description="二维码ID"),
    db: AsyncSession = Depends(get_database)
):
    """删除抽奖二维码"""
    try:
        await LotteryCodeService.delete_lottery_code(db, code_id)
        return CommonResponse.deleted("抽奖二维码删除成功")
    except Exception as e:
        logger.error(f"删除抽奖二维码失败: {str(e)}")
        raise APIException(f"删除抽奖二维码失败: {str(e)}")


@router.get("/", response_model=dict, summary="获取抽奖二维码列表")
async def get_lottery_codes(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    is_used: Optional[bool] = Query(None, description="是否已使用"),
    prize_level: Optional[int] = Query(None, ge=0, description="奖品等级"),
    db: AsyncSession = Depends(get_database)
):
    """获取抽奖二维码列表（分页）"""
    try:
        skip = (page - 1) * size
        codes, total = await LotteryCodeService.get_lottery_codes(
            db, skip=skip, limit=size, is_used=is_used, prize_level=prize_level
        )
        
        # 转换为响应模型
        code_responses = [LotteryCodeResponse.from_orm(code).dict() for code in codes]
        
        # 计算总页数
        pages = math.ceil(total / size) if total > 0 else 1
        
        code_list = LotteryCodeList(
            codes=code_responses,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
        return CommonResponse.success(data=code_list.dict(), message="获取抽奖二维码列表成功")
    except Exception as e:
        logger.error(f"获取抽奖二维码列表失败: {str(e)}")
        raise APIException(f"获取抽奖二维码列表失败: {str(e)}")


@router.post("/scan", response_model=dict, summary="扫码抽奖")
async def scan_lottery_code(
    scan_data: LotteryCodeScan,
    request: Request,
    db: AsyncSession = Depends(get_database)
):
    """扫码抽奖"""
    try:
        # 获取客户端IP
        client_ip = get_client_ip(request)
        
        # 执行扫码
        result = await LotteryCodeService.scan_lottery_code(db, scan_data.code, client_ip)
        
        # 构造响应
        scan_response = LotteryCodeScanResponse(**result)
        
        if result["success"]:
            return CommonResponse.success(data=scan_response.dict(), message=result["message"])
        else:
            return CommonResponse.bad_request(message=result["message"], data=scan_response.dict())
    
    except Exception as e:
        logger.error(f"扫码抽奖失败: {str(e)}")
        raise APIException(f"扫码抽奖失败: {str(e)}")


@router.get("/code/{code}", response_model=dict, summary="根据编码获取二维码信息")
async def get_lottery_code_by_code(
    code: str = Path(..., description="二维码编码"),
    db: AsyncSession = Depends(get_database)
):
    """根据编码获取抽奖二维码信息（不更新使用状态）"""
    try:
        lottery_code = await LotteryCodeService.get_code_by_code(db, code)
        if not lottery_code:
            return CommonResponse.not_found("抽奖二维码不存在")
        
        code_response = LotteryCodeResponse.from_orm(lottery_code)
        return CommonResponse.success(data=code_response.dict(), message="获取抽奖二维码成功")
    except Exception as e:
        logger.error(f"获取抽奖二维码失败: {str(e)}")
        raise APIException(f"获取抽奖二维码失败: {str(e)}")


@router.get("/stats/overview", response_model=dict, summary="获取抽奖统计信息")
async def get_lottery_stats(
    db: AsyncSession = Depends(get_database)
):
    """获取抽奖统计信息"""
    try:
        stats = await LotteryCodeService.get_lottery_stats(db)
        stats_response = LotteryCodeStats(**stats)
        return CommonResponse.success(data=stats_response.dict(), message="获取统计信息成功")
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise APIException(f"获取统计信息失败: {str(e)}")
