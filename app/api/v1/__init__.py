from fastapi import APIRouter
from .endpoints import health, users, auth, lottery_codes

api_router = APIRouter()

# 注册路由
api_router.include_router(health.router, prefix="/health", tags=["健康检查"])
api_router.include_router(auth.router, prefix="/auth", tags=["用户认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(lottery_codes.router, prefix="/lottery-codes", tags=["抽奖二维码"])

# 可以在这里添加更多路由
# api_router.include_router(activities.router, prefix="/activities", tags=["活动管理"])
