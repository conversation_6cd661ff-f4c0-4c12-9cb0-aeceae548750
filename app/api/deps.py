from typing import Generator
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.config.database import get_db


async def get_database() -> AsyncSession:
    """获取数据库会话依赖"""
    async for db in get_db():
        yield db


# 可以在这里添加其他依赖，比如：
# - 用户认证依赖
# - 权限检查依赖
# - 缓存依赖
# - 限流依赖等

# 示例：用户认证依赖（需要实现JWT认证后使用）
# async def get_current_user(
#     token: str = Depends(oauth2_scheme),
#     db: AsyncSession = Depends(get_database)
# ):
#     """获取当前用户"""
#     # 这里实现JWT token验证逻辑
#     pass
