import logging
from typing import Optional


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """获取日志器实例
    
    Args:
        name: 日志器名称，如果为None则使用调用模块的名称
    
    Returns:
        logging.Logger: 日志器实例
    """
    if name is None:
        # 获取调用者的模块名
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return logging.getLogger(name)


class LoggerMixin:
    """日志混入类，为类提供日志功能"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        return get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")


# 常用日志装饰器
def log_function_call(logger: Optional[logging.Logger] = None):
    """记录函数调用的装饰器"""
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            _logger = logger or get_logger(func.__module__)
            _logger.debug(f"调用函数: {func.__name__}, 参数: args={args}, kwargs={kwargs}")
            try:
                result = await func(*args, **kwargs)
                _logger.debug(f"函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                _logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            _logger = logger or get_logger(func.__module__)
            _logger.debug(f"调用函数: {func.__name__}, 参数: args={args}, kwargs={kwargs}")
            try:
                result = func(*args, **kwargs)
                _logger.debug(f"函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                _logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
                raise
        
        # 判断是否为异步函数
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
