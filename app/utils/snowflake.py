import time
import threading
from typing import Optional


class SnowflakeGenerator:
    """雪花ID生成器"""
    
    def __init__(self, datacenter_id: int = 1, worker_id: int = 1):
        # 时间戳位数
        self.timestamp_bits = 41
        # 数据中心ID位数
        self.datacenter_id_bits = 5
        # 机器ID位数
        self.worker_id_bits = 5
        # 序列号位数
        self.sequence_bits = 12
        
        # 最大值计算
        self.max_datacenter_id = (1 << self.datacenter_id_bits) - 1
        self.max_worker_id = (1 << self.worker_id_bits) - 1
        self.max_sequence = (1 << self.sequence_bits) - 1
        
        # 位移量
        self.worker_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits
        self.timestamp_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits
        
        # 验证参数
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"datacenter_id must be between 0 and {self.max_datacenter_id}")
        
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError(f"worker_id must be between 0 and {self.max_worker_id}")
        
        self.datacenter_id = datacenter_id
        self.worker_id = worker_id
        self.sequence = 0
        self.last_timestamp = -1
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 起始时间戳 (2023-01-01 00:00:00 UTC)
        self.epoch = 1672531200000
    
    def _current_timestamp(self) -> int:
        """获取当前时间戳（毫秒）"""
        return int(time.time() * 1000)
    
    def _wait_next_millis(self, last_timestamp: int) -> int:
        """等待下一毫秒"""
        timestamp = self._current_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._current_timestamp()
        return timestamp
    
    def generate_id(self) -> int:
        """生成雪花ID"""
        with self.lock:
            timestamp = self._current_timestamp()
            
            # 时间回拨检查
            if timestamp < self.last_timestamp:
                raise Exception(f"Clock moved backwards. Refusing to generate id for {self.last_timestamp - timestamp} milliseconds")
            
            # 同一毫秒内
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.max_sequence
                if self.sequence == 0:
                    # 序列号用完，等待下一毫秒
                    timestamp = self._wait_next_millis(self.last_timestamp)
            else:
                # 新的毫秒，序列号重置
                self.sequence = 0
            
            self.last_timestamp = timestamp
            
            # 生成ID
            snowflake_id = (
                ((timestamp - self.epoch) << self.timestamp_shift) |
                (self.datacenter_id << self.datacenter_id_shift) |
                (self.worker_id << self.worker_id_shift) |
                self.sequence
            )
            
            return snowflake_id


# 全局雪花ID生成器实例
snowflake_generator = SnowflakeGenerator(datacenter_id=1, worker_id=1)


def generate_snowflake_id() -> int:
    """生成雪花ID"""
    return snowflake_generator.generate_id()


def parse_snowflake_id(snowflake_id: int) -> dict:
    """解析雪花ID"""
    timestamp_shift = 22  # 12 + 5 + 5
    datacenter_id_shift = 17  # 12 + 5
    worker_id_shift = 12
    
    timestamp = (snowflake_id >> timestamp_shift) + snowflake_generator.epoch
    datacenter_id = (snowflake_id >> datacenter_id_shift) & ((1 << 5) - 1)
    worker_id = (snowflake_id >> worker_id_shift) & ((1 << 5) - 1)
    sequence = snowflake_id & ((1 << 12) - 1)
    
    return {
        "timestamp": timestamp,
        "datacenter_id": datacenter_id,
        "worker_id": worker_id,
        "sequence": sequence,
        "datetime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp / 1000))
    }
