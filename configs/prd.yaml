# 生产环境配置
app:
  name: "Activity Lottery API"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 8000
  reload: false

database:
  host: "***********"
  port: 3306
  username: "devops"
  password: "wldev%402022"
  database: "cloud_wecom"
  charset: "utf8mb4"
  pool_size: 20
  max_overflow: 40
  pool_timeout: 30
  pool_recycle: 3600

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/app_prd.log"
  max_bytes: 52428800  # 50MB
  backup_count: 10
  console_output: false

cors:
  allow_origins: ["https://yourdomain.com"]
  allow_credentials: true
  allow_methods: ["GET", "POST", "PUT", "DELETE"]
  allow_headers: ["*"]

security:
  secret_key: "dev-secret-key-change-in-production"
  algorithm: "HS256"
  access_token_expire_minutes: 60

