# 开发环境配置
app:
  name: "Activity Lottery API"
  version: "1.0.0"
  debug: true
  host: "0.0.0.0"
  port: 8000
  reload: true

database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "123456"
  database: "activity_lottery_dev"
  charset: "utf8mb4"
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/app_dev.log"
  max_bytes: 10485760  # 10MB
  backup_count: 5
  console_output: true

cors:
  allow_origins: ["*"]
  allow_credentials: true
  allow_methods: ["*"]
  allow_headers: ["*"]

security:
  secret_key: "dev-secret-key-change-in-production"
  algorithm: "HS256"
  access_token_expire_minutes: 30
