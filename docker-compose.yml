version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: activity-lottery-mysql
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: activity_lottery_dev
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: activity-lottery-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI应用
  app:
    build: .
    container_name: activity-lottery-app
    environment:
      ENVIRONMENT: dev
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: app_user
      DB_PASSWORD: app_password
      DB_NAME: activity_lottery_dev
      SECRET_KEY: dev-secret-key-for-docker
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
  redis_data:
