#!/usr/bin/env python3
"""
抽奖二维码奖品等级赋值脚本
基于Augment Context Engine检索的现有代码架构实现
支持多种赋值策略和条件筛选
"""
import asyncio
import os
import sys
import argparse
import json
import csv
from typing import List, Dict, Optional, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.config.database import AsyncSessionLocal
from app.services.lottery_code_service import LotteryCodeService
from app.schemas.lottery_code import LotteryCodeUpdate
from app.models.lottery_code import LotteryCode
from app.utils.logger import get_logger
from sqlalchemy import select, func, and_, or_, update
from sqlalchemy.ext.asyncio import AsyncSession

logger = get_logger(__name__)


class PrizeLevelAssigner:
    """奖品等级赋值器
    
    基于现有的LotteryCodeService和数据库架构实现
    支持多种赋值策略和条件筛选
    """
    
    def __init__(self):
        self.db_session = None
        self.assignment_log = []
        self.statistics = {
            "total_processed": 0,
            "total_updated": 0,
            "total_skipped": 0,
            "update_details": {},
            "start_time": None,
            "end_time": None,
            "processing_time": 0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.db_session = AsyncSessionLocal()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.db_session:
            await self.db_session.close()
    
    async def get_codes_by_criteria(self, criteria: Dict) -> List[LotteryCode]:
        """根据条件获取二维码列表
        
        Args:
            criteria: 筛选条件字典
        
        Returns:
            List[LotteryCode]: 符合条件的二维码列表
        """
        query = select(LotteryCode)
        conditions = []
        
        # 根据使用状态筛选
        if "is_used" in criteria:
            conditions.append(LotteryCode.is_used == criteria["is_used"])
        
        # 根据当前奖品等级筛选
        if "current_prize_level" in criteria:
            level = criteria["current_prize_level"]
            if isinstance(level, list):
                conditions.append(LotteryCode.prize_level.in_(level))
            else:
                conditions.append(LotteryCode.prize_level == level)
        
        # 根据ID范围筛选
        if "id_range" in criteria:
            id_min, id_max = criteria["id_range"]
            if id_min:
                conditions.append(LotteryCode.id >= id_min)
            if id_max:
                conditions.append(LotteryCode.id <= id_max)
        
        # 根据创建时间筛选
        if "created_after" in criteria:
            conditions.append(LotteryCode.create_at >= criteria["created_after"])
        if "created_before" in criteria:
            conditions.append(LotteryCode.create_at <= criteria["created_before"])
        
        # 根据编码模式筛选
        if "code_pattern" in criteria:
            pattern = criteria["code_pattern"]
            conditions.append(LotteryCode.code.like(f"%{pattern}%"))
        
        # 应用条件
        if conditions:
            query = query.where(and_(*conditions))
        
        # 添加排序和限制
        if "limit" in criteria:
            query = query.limit(criteria["limit"])
        
        query = query.order_by(LotteryCode.create_at.desc())
        
        result = await self.db_session.execute(query)
        return list(result.scalars().all())
    
    async def assign_prize_levels_by_strategy(
        self, 
        strategy: str, 
        target_prize_level: int,
        criteria: Dict = None,
        **kwargs
    ) -> int:
        """根据策略分配奖品等级
        
        Args:
            strategy: 分配策略
            target_prize_level: 目标奖品等级
            criteria: 筛选条件
            **kwargs: 策略参数
        
        Returns:
            int: 更新的记录数
        """
        self.statistics["start_time"] = datetime.now()
        criteria = criteria or {}
        
        print(f"🎯 开始执行奖品等级赋值...")
        print(f"   策略: {strategy}")
        print(f"   目标等级: {target_prize_level} ({LotteryCodeService.get_prize_name(target_prize_level)})")
        
        if strategy == "random":
            return await self._assign_random(target_prize_level, criteria, **kwargs)
        elif strategy == "sequential":
            return await self._assign_sequential(target_prize_level, criteria, **kwargs)
        elif strategy == "percentage":
            return await self._assign_percentage(target_prize_level, criteria, **kwargs)
        elif strategy == "specific_ids":
            return await self._assign_specific_ids(target_prize_level, **kwargs)
        elif strategy == "specific_codes":
            return await self._assign_specific_codes(target_prize_level, **kwargs)
        elif strategy == "batch_update":
            return await self._assign_batch_update(target_prize_level, criteria, **kwargs)
        else:
            raise ValueError(f"不支持的策略: {strategy}")
    
    async def _assign_random(self, target_level: int, criteria: Dict, **kwargs) -> int:
        """随机分配策略"""
        count = kwargs.get("count", 10)
        
        # 获取符合条件的二维码
        codes = await self.get_codes_by_criteria(criteria)
        
        if len(codes) < count:
            print(f"⚠️  符合条件的二维码数量({len(codes)})少于请求数量({count})")
            count = len(codes)
        
        # 随机选择
        import random
        selected_codes = random.sample(codes, count)
        
        # 批量更新
        updated_count = 0
        for code in selected_codes:
            old_level = code.prize_level
            code.prize_level = target_level
            
            self.assignment_log.append({
                "id": code.id,
                "code": code.code,
                "old_level": old_level,
                "new_level": target_level,
                "strategy": "random"
            })
            updated_count += 1
        
        await self.db_session.commit()
        print(f"✅ 随机分配完成，更新了 {updated_count} 个二维码")
        return updated_count
    
    async def _assign_sequential(self, target_level: int, criteria: Dict, **kwargs) -> int:
        """顺序分配策略"""
        count = kwargs.get("count", 10)
        
        # 获取符合条件的二维码（按创建时间排序）
        criteria["limit"] = count
        codes = await self.get_codes_by_criteria(criteria)
        
        # 顺序更新
        updated_count = 0
        for code in codes:
            old_level = code.prize_level
            code.prize_level = target_level
            
            self.assignment_log.append({
                "id": code.id,
                "code": code.code,
                "old_level": old_level,
                "new_level": target_level,
                "strategy": "sequential"
            })
            updated_count += 1
        
        await self.db_session.commit()
        print(f"✅ 顺序分配完成，更新了 {updated_count} 个二维码")
        return updated_count
    
    async def _assign_percentage(self, target_level: int, criteria: Dict, **kwargs) -> int:
        """按百分比分配策略"""
        percentage = kwargs.get("percentage", 0.1)  # 默认10%
        
        # 获取符合条件的二维码总数
        codes = await self.get_codes_by_criteria(criteria)
        total_count = len(codes)
        target_count = int(total_count * percentage)
        
        print(f"   总数: {total_count}, 目标更新数: {target_count} ({percentage*100:.1f}%)")
        
        # 随机选择指定百分比的二维码
        import random
        selected_codes = random.sample(codes, min(target_count, total_count))
        
        # 批量更新
        updated_count = 0
        for code in selected_codes:
            old_level = code.prize_level
            code.prize_level = target_level
            
            self.assignment_log.append({
                "id": code.id,
                "code": code.code,
                "old_level": old_level,
                "new_level": target_level,
                "strategy": "percentage"
            })
            updated_count += 1
        
        await self.db_session.commit()
        print(f"✅ 按百分比分配完成，更新了 {updated_count} 个二维码")
        return updated_count
    
    async def _assign_specific_ids(self, target_level: int, **kwargs) -> int:
        """指定ID分配策略"""
        ids = kwargs.get("ids", [])
        if not ids:
            print("❌ 未提供ID列表")
            return 0
        
        # 查询指定ID的二维码
        query = select(LotteryCode).where(LotteryCode.id.in_(ids))
        result = await self.db_session.execute(query)
        codes = list(result.scalars().all())
        
        # 批量更新
        updated_count = 0
        for code in codes:
            old_level = code.prize_level
            code.prize_level = target_level
            
            self.assignment_log.append({
                "id": code.id,
                "code": code.code,
                "old_level": old_level,
                "new_level": target_level,
                "strategy": "specific_ids"
            })
            updated_count += 1
        
        await self.db_session.commit()
        print(f"✅ 指定ID分配完成，更新了 {updated_count} 个二维码")
        return updated_count
    
    async def _assign_specific_codes(self, target_level: int, **kwargs) -> int:
        """指定编码分配策略"""
        codes_list = kwargs.get("codes", [])
        if not codes_list:
            print("❌ 未提供编码列表")
            return 0
        
        # 查询指定编码的二维码
        query = select(LotteryCode).where(LotteryCode.code.in_(codes_list))
        result = await self.db_session.execute(query)
        codes = list(result.scalars().all())
        
        # 批量更新
        updated_count = 0
        for code in codes:
            old_level = code.prize_level
            code.prize_level = target_level
            
            self.assignment_log.append({
                "id": code.id,
                "code": code.code,
                "old_level": old_level,
                "new_level": target_level,
                "strategy": "specific_codes"
            })
            updated_count += 1
        
        await self.db_session.commit()
        print(f"✅ 指定编码分配完成，更新了 {updated_count} 个二维码")
        return updated_count
    
    async def _assign_batch_update(self, target_level: int, criteria: Dict, **kwargs) -> int:
        """批量更新策略（使用SQL批量更新，性能更好）"""
        # 构建更新条件
        conditions = []
        
        if "is_used" in criteria:
            conditions.append(LotteryCode.is_used == criteria["is_used"])
        
        if "current_prize_level" in criteria:
            level = criteria["current_prize_level"]
            if isinstance(level, list):
                conditions.append(LotteryCode.prize_level.in_(level))
            else:
                conditions.append(LotteryCode.prize_level == level)
        
        if "id_range" in criteria:
            id_min, id_max = criteria["id_range"]
            if id_min:
                conditions.append(LotteryCode.id >= id_min)
            if id_max:
                conditions.append(LotteryCode.id <= id_max)
        
        # 执行批量更新
        if conditions:
            stmt = (
                update(LotteryCode)
                .where(and_(*conditions))
                .values(prize_level=target_level)
            )
            
            result = await self.db_session.execute(stmt)
            await self.db_session.commit()
            
            updated_count = result.rowcount
            print(f"✅ 批量更新完成，更新了 {updated_count} 个二维码")
            return updated_count
        else:
            print("❌ 未提供有效的更新条件")
            return 0
    
    async def get_assignment_preview(self, strategy: str, criteria: Dict = None, **kwargs) -> Dict:
        """预览分配结果（不实际执行）"""
        criteria = criteria or {}
        
        # 获取符合条件的二维码
        codes = await self.get_codes_by_criteria(criteria)
        total_count = len(codes)
        
        preview = {
            "total_matching": total_count,
            "will_be_updated": 0,
            "current_distribution": {},
            "after_distribution": {}
        }
        
        # 统计当前分布
        for code in codes:
            level = code.prize_level
            preview["current_distribution"][level] = preview["current_distribution"].get(level, 0) + 1
        
        # 计算更新数量
        if strategy == "random" or strategy == "sequential":
            count = kwargs.get("count", 10)
            preview["will_be_updated"] = min(count, total_count)
        elif strategy == "percentage":
            percentage = kwargs.get("percentage", 0.1)
            preview["will_be_updated"] = int(total_count * percentage)
        elif strategy == "specific_ids":
            ids = kwargs.get("ids", [])
            preview["will_be_updated"] = len(ids)
        elif strategy == "specific_codes":
            codes_list = kwargs.get("codes", [])
            preview["will_be_updated"] = len(codes_list)
        elif strategy == "batch_update":
            preview["will_be_updated"] = total_count
        
        return preview
    
    def _update_statistics(self):
        """更新统计信息"""
        self.statistics["end_time"] = datetime.now()
        self.statistics["total_processed"] = len(self.assignment_log)
        self.statistics["total_updated"] = len([log for log in self.assignment_log if log.get("old_level") != log.get("new_level")])
        self.statistics["total_skipped"] = self.statistics["total_processed"] - self.statistics["total_updated"]
        self.statistics["processing_time"] = (
            self.statistics["end_time"] - self.statistics["start_time"]
        ).total_seconds()
        
        # 统计更新详情
        update_details = {}
        for log in self.assignment_log:
            old_level = log["old_level"]
            new_level = log["new_level"]
            key = f"{old_level}->{new_level}"
            update_details[key] = update_details.get(key, 0) + 1
        
        self.statistics["update_details"] = update_details
    
    async def export_assignment_log(self, filename: str = None):
        """导出分配日志"""
        if not self.assignment_log:
            print("❌ 没有分配日志可导出")
            return
        
        if not filename:
            filename = f"prize_assignment_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        print(f"📄 导出分配日志到: {filename}")
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['id', 'code', 'old_level', 'old_prize_name', 'new_level', 'new_prize_name', 'strategy', 'timestamp']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for log in self.assignment_log:
                row = {
                    'id': log['id'],
                    'code': log['code'],
                    'old_level': log['old_level'],
                    'old_prize_name': LotteryCodeService.get_prize_name(log['old_level']),
                    'new_level': log['new_level'],
                    'new_prize_name': LotteryCodeService.get_prize_name(log['new_level']),
                    'strategy': log['strategy'],
                    'timestamp': datetime.now().isoformat()
                }
                writer.writerow(row)
        
        print(f"✅ 日志导出完成: {filename}")
        return filename
    
    def print_statistics(self):
        """打印统计信息"""
        self._update_statistics()
        
        print("\n📊 分配统计信息:")
        print(f"   处理记录数: {self.statistics['total_processed']}")
        print(f"   实际更新数: {self.statistics['total_updated']}")
        print(f"   跳过记录数: {self.statistics['total_skipped']}")
        print(f"   处理耗时: {self.statistics['processing_time']:.2f}秒")
        
        if self.statistics['update_details']:
            print("\n🔄 更新详情:")
            for change, count in self.statistics['update_details'].items():
                old_level, new_level = change.split('->')
                old_name = LotteryCodeService.get_prize_name(int(old_level))
                new_name = LotteryCodeService.get_prize_name(int(new_level))
                print(f"   {old_name} -> {new_name}: {count}个")


def parse_criteria(criteria_str: str) -> Dict:
    """解析筛选条件字符串"""
    if not criteria_str:
        return {}
    
    try:
        # 尝试解析JSON格式
        if criteria_str.startswith('{'):
            return json.loads(criteria_str)
        
        # 解析简单格式: key=value,key2=value2
        criteria = {}
        parts = criteria_str.split(',')
        for part in parts:
            if '=' in part:
                key, value = part.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # 特殊处理某些字段
                if key == "is_used":
                    criteria[key] = value.lower() in ['true', '1', 'yes']
                elif key in ["current_prize_level", "limit"]:
                    try:
                        criteria[key] = int(value)
                    except ValueError:
                        # 可能是列表格式
                        if '[' in value and ']' in value:
                            criteria[key] = json.loads(value)
                        else:
                            criteria[key] = value
                elif key == "id_range":
                    # 格式: min:max
                    if ':' in value:
                        min_id, max_id = value.split(':')
                        criteria[key] = [int(min_id) if min_id else None, int(max_id) if max_id else None]
                else:
                    criteria[key] = value
        
        return criteria
    except Exception as e:
        print(f"⚠️  条件解析失败: {e}")
        return {}


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="抽奖二维码奖品等级赋值工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
策略说明:
  random      - 随机选择指定数量的二维码
  sequential  - 按创建时间顺序选择
  percentage  - 按百分比选择
  specific_ids - 指定ID列表
  specific_codes - 指定编码列表
  batch_update - 批量更新（高性能）

示例用法:
  python scripts/assign_prize_levels.py 1 --strategy random --count 100
  python scripts/assign_prize_levels.py 2 --strategy percentage --percentage 0.05 --criteria "is_used=false"
  python scripts/assign_prize_levels.py 1 --strategy specific_ids --ids "123,456,789"
        """
    )
    
    parser.add_argument("prize_level", type=int, help="目标奖品等级")
    parser.add_argument("--strategy", required=True, 
                       choices=["random", "sequential", "percentage", "specific_ids", "specific_codes", "batch_update"],
                       help="分配策略")
    parser.add_argument("--count", type=int, help="数量 (用于random/sequential策略)")
    parser.add_argument("--percentage", type=float, help="百分比 (用于percentage策略)")
    parser.add_argument("--ids", help="ID列表，逗号分隔 (用于specific_ids策略)")
    parser.add_argument("--codes", help="编码列表，逗号分隔 (用于specific_codes策略)")
    parser.add_argument("--criteria", help="筛选条件 (JSON格式或简单格式)")
    parser.add_argument("--preview", action="store_true", help="仅预览，不实际执行")
    parser.add_argument("--export-log", help="导出分配日志文件名")
    
    args = parser.parse_args()
    
    # 验证参数
    if args.prize_level < 0:
        print("❌ 奖品等级不能为负数")
        return
    
    # 解析筛选条件
    criteria = parse_criteria(args.criteria) if args.criteria else {}
    
    # 准备策略参数
    strategy_kwargs = {}
    if args.count:
        strategy_kwargs["count"] = args.count
    if args.percentage:
        strategy_kwargs["percentage"] = args.percentage
    if args.ids:
        strategy_kwargs["ids"] = [int(id.strip()) for id in args.ids.split(',')]
    if args.codes:
        strategy_kwargs["codes"] = [code.strip() for code in args.codes.split(',')]
    
    print("🎰 抽奖二维码奖品等级赋值工具")
    print("=" * 60)
    print(f"目标等级: {args.prize_level} ({LotteryCodeService.get_prize_name(args.prize_level)})")
    print(f"分配策略: {args.strategy}")
    print(f"筛选条件: {criteria}")
    print(f"策略参数: {strategy_kwargs}")
    print("=" * 60)
    
    try:
        async with PrizeLevelAssigner() as assigner:
            # 预览模式
            if args.preview:
                preview = await assigner.get_assignment_preview(args.strategy, criteria, **strategy_kwargs)
                print("\n📋 分配预览:")
                print(f"   符合条件的总数: {preview['total_matching']}")
                print(f"   将要更新的数量: {preview['will_be_updated']}")
                print(f"   当前奖品分布: {preview['current_distribution']}")
                return
            
            # 确认执行
            if strategy_kwargs.get("count", 0) > 1000 or strategy_kwargs.get("percentage", 0) > 0.1:
                confirm = input("⚠️  这将更新大量数据，确认继续？(y/N): ")
                if confirm.lower() != 'y':
                    print("操作已取消")
                    return
            
            # 执行分配
            updated_count = await assigner.assign_prize_levels_by_strategy(
                args.strategy, args.prize_level, criteria, **strategy_kwargs
            )
            
            # 打印统计信息
            assigner.print_statistics()
            
            # 导出日志
            if args.export_log:
                await assigner.export_assignment_log(args.export_log)
            elif updated_count > 0:
                export_choice = input("\n是否导出分配日志？(y/N): ")
                if export_choice.lower() == 'y':
                    await assigner.export_assignment_log()
            
            print(f"\n🎉 奖品等级赋值完成！共更新 {updated_count} 个二维码")
            
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {str(e)}")
        logger.error(f"奖品等级赋值失败: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
