#!/usr/bin/env python3
"""
批量生成抽奖二维码脚本
支持灵活的配置和多种生成策略
"""
import asyncio
import os
import sys
import argparse
import json
import csv
from typing import List, Dict, Optional
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.config.database import AsyncSessionLocal
from app.services.lottery_code_service import LotteryCodeService
from app.schemas.lottery_code import LotteryCodeBatchCreate
from app.utils.snowflake import generate_snowflake_id, parse_snowflake_id
from app.utils.logger import get_logger

logger = get_logger(__name__)


class BatchCodeGenerator:
    """批量二维码生成器"""
    
    def __init__(self):
        self.db_session = None
        self.generated_codes = []
        self.statistics = {
            "total_requested": 0,
            "total_generated": 0,
            "generation_time": 0,
            "prize_distribution": {},
            "start_time": None,
            "end_time": None
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.db_session = AsyncSessionLocal()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.db_session:
            await self.db_session.close()
    
    def setup_prize_distribution(self, total_count: int, config: Dict) -> List[int]:
        """设置奖品分配策略"""
        prize_levels = []
        
        if "prize_config" in config:
            # 使用配置文件中的奖品分配
            prize_config = config["prize_config"]
            for level, info in prize_config.items():
                count = info.get("count", 0)
                if isinstance(count, float):  # 百分比
                    count = int(total_count * count)
                prize_levels.extend([int(level)] * count)
        
        elif "prize_ratios" in config:
            # 使用比例分配
            ratios = config["prize_ratios"]
            for level, ratio in ratios.items():
                count = int(total_count * ratio)
                prize_levels.extend([int(level)] * count)
        
        elif "prize_levels" in config:
            # 直接使用奖品等级列表
            prize_levels = config["prize_levels"]
        
        else:
            # 默认分配：90%谢谢参与，10%中奖
            no_prize_count = int(total_count * 0.9)
            prize_count = total_count - no_prize_count
            prize_levels = [0] * no_prize_count + [1] * prize_count
        
        # 如果生成的奖品数量不足，用谢谢参与补齐
        while len(prize_levels) < total_count:
            prize_levels.append(0)
        
        # 如果生成的奖品数量过多，截取
        if len(prize_levels) > total_count:
            prize_levels = prize_levels[:total_count]
        
        return prize_levels
    
    def generate_url_template(self, config: Dict) -> str:
        """生成URL模板"""
        if "url_template" in config:
            return config["url_template"]
        
        base_url = config.get("base_url", "https://example.com/lottery")
        if not base_url.endswith("/"):
            base_url += "/"
        
        return f"{base_url}{{code}}"
    
    async def generate_batch(self, count: int, config: Dict) -> List[Dict]:
        """批量生成二维码"""
        self.statistics["start_time"] = datetime.now()
        self.statistics["total_requested"] = count
        
        print(f"🎯 开始批量生成 {count} 个抽奖二维码...")
        
        # 设置奖品分配
        prize_levels = self.setup_prize_distribution(count, config)
        
        # 生成URL模板
        url_template = self.generate_url_template(config)
        
        print(f"📋 奖品分配策略:")
        prize_stats = {}
        for level in prize_levels:
            prize_stats[level] = prize_stats.get(level, 0) + 1
        
        for level, count_level in sorted(prize_stats.items()):
            prize_name = LotteryCodeService.get_prize_name(level)
            percentage = (count_level / count) * 100
            print(f"   {prize_name}: {count_level}个 ({percentage:.1f}%)")
        
        # 创建批量数据
        batch_data = LotteryCodeBatchCreate(
            count=count,
            url_template=url_template,
            prize_levels=prize_levels
        )
        
        try:
            # 执行批量创建
            lottery_codes = await LotteryCodeService.batch_create_lottery_codes(
                self.db_session, batch_data
            )
            
            # 转换为字典格式
            self.generated_codes = []
            for code in lottery_codes:
                code_dict = {
                    "id": code.id,
                    "code": code.code,
                    "url": code.url,
                    "prize_level": code.prize_level,
                    "prize_name": LotteryCodeService.get_prize_name(code.prize_level),
                    "is_used": code.is_used,
                    "create_at": code.create_at.isoformat() if code.create_at else None,
                    "snowflake_info": parse_snowflake_id(code.id)
                }
                self.generated_codes.append(code_dict)
            
            self.statistics["total_generated"] = len(self.generated_codes)
            self.statistics["prize_distribution"] = prize_stats
            self.statistics["end_time"] = datetime.now()
            self.statistics["generation_time"] = (
                self.statistics["end_time"] - self.statistics["start_time"]
            ).total_seconds()
            
            print(f"✅ 批量生成完成！")
            print(f"   生成数量: {self.statistics['total_generated']}")
            print(f"   耗时: {self.statistics['generation_time']:.2f}秒")
            print(f"   平均速度: {self.statistics['total_generated']/self.statistics['generation_time']:.1f}个/秒")
            
            return self.generated_codes
            
        except Exception as e:
            logger.error(f"批量生成失败: {str(e)}")
            raise
    
    async def export_to_csv(self, filename: str):
        """导出到CSV文件"""
        if not self.generated_codes:
            print("❌ 没有可导出的数据")
            return
        
        print(f"📄 导出数据到CSV文件: {filename}")
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'id', 'code', 'url', 'prize_level', 'prize_name', 
                'is_used', 'create_at', 'qr_link'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for code_data in self.generated_codes:
                row = {
                    'id': code_data['id'],
                    'code': code_data['code'],
                    'url': code_data['url'],
                    'prize_level': code_data['prize_level'],
                    'prize_name': code_data['prize_name'],
                    'is_used': code_data['is_used'],
                    'create_at': code_data['create_at'],
                    'qr_link': f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={code_data['url']}"
                }
                writer.writerow(row)
        
        print(f"✅ CSV导出完成: {filename}")
    
    async def export_to_json(self, filename: str):
        """导出到JSON文件"""
        if not self.generated_codes:
            print("❌ 没有可导出的数据")
            return
        
        print(f"📄 导出数据到JSON文件: {filename}")
        
        export_data = {
            "metadata": {
                "export_time": datetime.now().isoformat(),
                "total_codes": len(self.generated_codes),
                "statistics": self.statistics
            },
            "codes": self.generated_codes
        }
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON导出完成: {filename}")
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n📊 生成统计信息:")
        print(f"   请求生成数量: {self.statistics['total_requested']}")
        print(f"   实际生成数量: {self.statistics['total_generated']}")
        print(f"   生成耗时: {self.statistics['generation_time']:.2f}秒")
        
        if self.statistics['generation_time'] > 0:
            speed = self.statistics['total_generated'] / self.statistics['generation_time']
            print(f"   生成速度: {speed:.1f}个/秒")
        
        print(f"   开始时间: {self.statistics['start_time']}")
        print(f"   结束时间: {self.statistics['end_time']}")
        
        print("\n🎁 奖品分布:")
        for level, count in sorted(self.statistics['prize_distribution'].items()):
            prize_name = LotteryCodeService.get_prize_name(level)
            percentage = (count / self.statistics['total_generated']) * 100
            print(f"   {prize_name}: {count}个 ({percentage:.1f}%)")


def load_config(config_file: str) -> Dict:
    """加载配置文件"""
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return {}
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            if config_file.endswith('.json'):
                return json.load(f)
            else:
                # 简单的键值对格式
                config = {}
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip()
                return config
    except Exception as e:
        print(f"❌ 加载配置文件失败: {str(e)}")
        return {}


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量生成抽奖二维码")
    parser.add_argument("count", type=int, help="生成数量")
    parser.add_argument("--config", "-c", help="配置文件路径")
    parser.add_argument("--base-url", help="基础URL")
    parser.add_argument("--url-template", help="URL模板")
    parser.add_argument("--export-csv", help="导出CSV文件名")
    parser.add_argument("--export-json", help="导出JSON文件名")
    parser.add_argument("--prize-ratio", help="中奖比例 (0.0-1.0)", type=float, default=0.1)
    
    args = parser.parse_args()
    
    # 验证参数
    if args.count <= 0:
        print("❌ 生成数量必须大于0")
        return
    
    if args.count > 10000:
        confirm = input(f"⚠️  您要生成 {args.count} 个二维码，这可能需要较长时间。确认继续？(y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
    
    # 加载配置
    config = {}
    if args.config:
        config = load_config(args.config)
    
    # 命令行参数覆盖配置文件
    if args.base_url:
        config["base_url"] = args.base_url
    if args.url_template:
        config["url_template"] = args.url_template
    if args.prize_ratio:
        config["prize_ratios"] = {"0": 1 - args.prize_ratio, "1": args.prize_ratio}
    
    # 设置默认值
    if "base_url" not in config and "url_template" not in config:
        config["base_url"] = "https://example.com/lottery"
    
    print("🎰 抽奖二维码批量生成器")
    print("=" * 50)
    print(f"生成数量: {args.count}")
    print(f"配置信息: {config}")
    print("=" * 50)
    
    try:
        async with BatchCodeGenerator() as generator:
            # 批量生成
            codes = await generator.generate_batch(args.count, config)
            
            # 打印统计信息
            generator.print_statistics()
            
            # 导出文件
            if args.export_csv:
                await generator.export_to_csv(args.export_csv)
            
            if args.export_json:
                await generator.export_to_json(args.export_json)
            
            # 如果没有指定导出文件，询问是否导出
            if not args.export_csv and not args.export_json:
                export_choice = input("\n是否导出生成的数据？(csv/json/n): ").lower()
                if export_choice == 'csv':
                    filename = f"lottery_codes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                    await generator.export_to_csv(filename)
                elif export_choice == 'json':
                    filename = f"lottery_codes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    await generator.export_to_json(filename)
            
            print(f"\n🎉 批量生成完成！共生成 {len(codes)} 个抽奖二维码")
            
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {str(e)}")
        logger.error(f"批量生成失败: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
