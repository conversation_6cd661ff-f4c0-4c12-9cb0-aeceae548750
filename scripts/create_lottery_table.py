#!/usr/bin/env python3
"""
创建抽奖二维码表脚本
"""
import asyncio
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.config.database import AsyncSessionLocal
from sqlalchemy import text


async def create_lottery_code_table():
    """创建抽奖二维码表"""
    print("正在创建抽奖二维码表...")
    
    # 创建表的SQL语句
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS lottery_code (
        id BIGINT NOT NULL PRIMARY KEY COMMENT '雪花ID',
        code VARCHAR(32) NOT NULL UNIQUE COMMENT '唯一编码(UUID去除-)',
        url VARCHAR(255) NOT NULL COMMENT '二维码链接',
        prize_level INT DEFAULT 0 NOT NULL COMMENT '中奖等级',
        is_used TINYINT(1) DEFAULT 0 NOT NULL COMMENT '是否使用过',
        ip VARCHAR(32) NULL COMMENT 'IP地址',
        scan_time DATETIME NULL COMMENT '扫码时间',
        create_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_code (code),
        INDEX idx_prize_level (prize_level),
        INDEX idx_is_used (is_used),
        INDEX idx_scan_time (scan_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖二维码链接';
    """
    
    try:
        async with AsyncSessionLocal() as db:
            # 执行创建表语句
            await db.execute(text(create_table_sql))
            await db.commit()
            
            print("✅ 抽奖二维码表创建成功！")
            
            # 验证表是否创建成功
            result = await db.execute(text("SHOW TABLES LIKE 'lottery_code'"))
            if result.fetchone():
                print("✅ 表验证成功，lottery_code表已存在")
                
                # 显示表结构
                result = await db.execute(text("DESCRIBE lottery_code"))
                columns = result.fetchall()
                print("\n📋 表结构信息:")
                print("字段名        | 类型           | 是否为空 | 键   | 默认值")
                print("-" * 60)
                for col in columns:
                    print(f"{col[0]:<12} | {col[1]:<14} | {col[2]:<8} | {col[3]:<4} | {col[4] or ''}")
            else:
                print("❌ 表验证失败，lottery_code表不存在")
                
    except Exception as e:
        print(f"❌ 创建抽奖二维码表失败: {str(e)}")


async def insert_sample_data():
    """插入示例数据"""
    print("\n正在插入示例数据...")
    
    try:
        async with AsyncSessionLocal() as db:
            # 检查是否已有数据
            result = await db.execute(text("SELECT COUNT(*) FROM lottery_code"))
            count = result.scalar()
            
            if count > 0:
                print(f"表中已有 {count} 条数据，跳过示例数据插入")
                return
            
            # 插入示例数据
            sample_data_sql = """
            INSERT INTO lottery_code (id, code, url, prize_level, is_used) VALUES
            (1, 'a1b2c3d4e5f6789012345678901234ab', 'https://example.com/qr/a1b2c3d4e5f6789012345678901234ab', 1, 0),
            (2, 'b2c3d4e5f6789012345678901234abc1', 'https://example.com/qr/b2c3d4e5f6789012345678901234abc1', 0, 0),
            (3, 'c3d4e5f6789012345678901234abc12b', 'https://example.com/qr/c3d4e5f6789012345678901234abc12b', 2, 0),
            (4, 'd4e5f6789012345678901234abc12bc3', 'https://example.com/qr/d4e5f6789012345678901234abc12bc3', 0, 1),
            (5, 'e5f6789012345678901234abc12bc3d4', 'https://example.com/qr/e5f6789012345678901234abc12bc3d4', 3, 0);
            """
            
            await db.execute(text(sample_data_sql))
            await db.commit()
            
            print("✅ 示例数据插入成功！")
            print("   - 插入了5条示例二维码数据")
            print("   - 包含不同奖品等级：1等奖、2等奖、3等奖、谢谢参与")
            print("   - 其中1条已标记为已使用")
            
    except Exception as e:
        print(f"❌ 插入示例数据失败: {str(e)}")


async def main():
    """主函数"""
    print("=== 抽奖二维码表初始化 ===")
    
    # 创建表
    await create_lottery_code_table()
    
    # 询问是否插入示例数据
    while True:
        choice = input("\n是否插入示例数据？(y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            await insert_sample_data()
            break
        elif choice in ['n', 'no']:
            print("跳过示例数据插入")
            break
        else:
            print("请输入 y 或 n")
    
    print("\n🎉 初始化完成！")
    print("现在您可以使用以下API测试抽奖功能：")
    print("- GET /api/v1/lottery-codes/ - 获取二维码列表")
    print("- POST /api/v1/lottery-codes/scan - 扫码抽奖")
    print("- GET /api/v1/lottery-codes/stats/overview - 查看统计信息")


if __name__ == "__main__":
    asyncio.run(main())
