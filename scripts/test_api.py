#!/usr/bin/env python3
"""
API测试脚本
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any


class APITester:
    """API测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.token = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = kwargs.get('headers', {})
        
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        
        kwargs['headers'] = headers
        
        async with self.session.request(method, url, **kwargs) as response:
            try:
                data = await response.json()
            except:
                data = {"error": "Invalid JSON response"}
            
            return {
                "status": response.status,
                "data": data
            }
    
    async def test_health_check(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        
        # 基础健康检查
        result = await self.request("GET", "/api/v1/health/")
        print(f"  基础健康检查: {result['status']} - {result['data'].get('message', '')}")
        
        # 详细健康检查
        result = await self.request("GET", "/api/v1/health/detailed")
        print(f"  详细健康检查: {result['status']} - {result['data'].get('message', '')}")
        
        # Ping测试
        result = await self.request("GET", "/api/v1/health/ping")
        print(f"  Ping测试: {result['status']} - {result['data'].get('data', '')}")
    
    async def test_user_registration(self):
        """测试用户注册"""
        print("\n👤 测试用户注册...")
        
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        
        result = await self.request(
            "POST", 
            "/api/v1/users/",
            json=user_data
        )
        
        print(f"  用户注册: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 201:
            user_info = result['data'].get('data', {})
            print(f"  创建的用户ID: {user_info.get('id')}")
            return user_info.get('id')
        
        return None
    
    async def test_user_login(self):
        """测试用户登录"""
        print("\n🔐 测试用户登录...")
        
        # 表单登录
        login_data = {
            "username": "testuser",
            "password": "testpassword123"
        }
        
        result = await self.request(
            "POST",
            "/api/v1/auth/login",
            data=login_data
        )
        
        print(f"  表单登录: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 200:
            token_info = result['data'].get('data', {})
            self.token = token_info.get('access_token')
            print(f"  获取到令牌: {self.token[:20]}...")
            return True
        
        return False
    
    async def test_protected_endpoint(self):
        """测试受保护的端点"""
        print("\n🛡️ 测试受保护的端点...")
        
        if not self.token:
            print("  没有令牌，跳过测试")
            return
        
        result = await self.request("GET", "/api/v1/auth/me")
        print(f"  获取当前用户信息: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 200:
            user_info = result['data'].get('data', {})
            print(f"  当前用户: {user_info.get('username')} ({user_info.get('email')})")
    
    async def test_user_list(self):
        """测试用户列表"""
        print("\n📋 测试用户列表...")
        
        result = await self.request("GET", "/api/v1/users/?page=1&size=10")
        print(f"  获取用户列表: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 200:
            user_list = result['data'].get('data', {})
            print(f"  用户总数: {user_list.get('total', 0)}")
            print(f"  当前页用户数: {len(user_list.get('users', []))}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API测试...")
        print("=" * 50)
        
        try:
            # 健康检查
            await self.test_health_check()
            
            # 用户注册
            user_id = await self.test_user_registration()
            
            # 用户登录
            login_success = await self.test_user_login()
            
            # 受保护端点测试
            if login_success:
                await self.test_protected_endpoint()
            
            # 用户列表
            await self.test_user_list()
            
            print("\n" + "=" * 50)
            print("✅ 所有测试完成！")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {str(e)}")


async def main():
    """主函数"""
    async with APITester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
