#!/usr/bin/env python3
"""
开发环境启动脚本
"""
import os
import sys
import uvicorn

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置环境变量
os.environ.setdefault("ENVIRONMENT", "dev")

if __name__ == "__main__":
    # 导入配置
    from app.config.settings import settings
    
    print(f"🚀 启动 {settings.app.name} 开发服务器")
    print(f"📍 环境: {settings.environment}")
    print(f"🌐 地址: http://{settings.app.host}:{settings.app.port}")
    print(f"📚 文档: http://{settings.app.host}:{settings.app.port}/docs")
    print("=" * 50)
    
    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.reload,
        log_level="debug" if settings.app.debug else "info",
        access_log=True,
    )
