#!/usr/bin/env python3
"""
创建超级用户脚本
"""
import asyncio
import os
import sys
from getpass import getpass

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.config.database import AsyncSessionLocal
from app.models.user import User
from app.services.user_service import UserService
from app.schemas.user import UserCreate


async def create_superuser():
    """创建超级用户"""
    print("=== 创建超级用户 ===")
    
    # 获取用户输入
    username = input("用户名: ").strip()
    if not username:
        print("用户名不能为空")
        return
    
    email = input("邮箱: ").strip()
    if not email:
        print("邮箱不能为空")
        return
    
    password = getpass("密码: ").strip()
    if not password:
        print("密码不能为空")
        return
    
    password_confirm = getpass("确认密码: ").strip()
    if password != password_confirm:
        print("两次输入的密码不一致")
        return
    
    full_name = input("全名 (可选): ").strip() or None
    
    try:
        async with AsyncSessionLocal() as db:
            # 检查用户是否已存在
            existing_user = await UserService.get_user_by_username(db, username)
            if existing_user:
                print(f"用户名 '{username}' 已存在")
                return
            
            existing_email = await UserService.get_user_by_email(db, email)
            if existing_email:
                print(f"邮箱 '{email}' 已存在")
                return
            
            # 创建超级用户
            user_data = UserCreate(
                username=username,
                email=email,
                password=password,
                full_name=full_name,
                is_active=True
            )
            
            user = await UserService.create_user(db, user_data)
            
            # 设置为超级用户
            user.is_superuser = True
            await db.commit()
            
            print(f"✅ 超级用户 '{username}' 创建成功！")
            print(f"   ID: {user.id}")
            print(f"   邮箱: {user.email}")
            print(f"   全名: {user.full_name or '未设置'}")
            
    except Exception as e:
        print(f"❌ 创建超级用户失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(create_superuser())
