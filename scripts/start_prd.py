#!/usr/bin/env python3
"""
生产环境启动脚本
"""
import os
import sys
import uvicorn

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置环境变量
os.environ.setdefault("ENVIRONMENT", "prd")

if __name__ == "__main__":
    # 导入配置
    from app.config.settings import settings
    
    print(f"🚀 启动 {settings.app.name} 生产服务器")
    print(f"📍 环境: {settings.environment}")
    print(f"🌐 地址: http://{settings.app.host}:{settings.app.port}")
    print("=" * 50)
    
    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.reload,
        log_level="info",
        access_log=False,  # 生产环境关闭访问日志以提高性能
        workers=1,  # 可以根据需要调整worker数量
    )
