#!/usr/bin/env python3
"""
数据库管理脚本
"""
import asyncio
import os
import sys
import argparse

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.config.database import engine, Base, AsyncSessionLocal
from app.models import *  # 导入所有模型
from sqlalchemy import text


async def create_tables():
    """创建所有表"""
    print("正在创建数据库表...")
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✅ 数据库表创建成功！")
    except Exception as e:
        print(f"❌ 创建数据库表失败: {str(e)}")


async def drop_tables():
    """删除所有表"""
    print("正在删除数据库表...")
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        print("✅ 数据库表删除成功！")
    except Exception as e:
        print(f"❌ 删除数据库表失败: {str(e)}")


async def reset_tables():
    """重置数据库表（删除后重新创建）"""
    print("正在重置数据库表...")
    await drop_tables()
    await create_tables()
    print("✅ 数据库表重置完成！")


async def show_tables():
    """显示所有表"""
    print("数据库表列表:")
    try:
        async with AsyncSessionLocal() as db:
            result = await db.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            if tables:
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("  没有找到任何表")
    except Exception as e:
        print(f"❌ 获取表列表失败: {str(e)}")


async def check_connection():
    """检查数据库连接"""
    print("正在检查数据库连接...")
    try:
        async with AsyncSessionLocal() as db:
            result = await db.execute(text("SELECT 1"))
            if result:
                print("✅ 数据库连接正常！")
            else:
                print("❌ 数据库连接异常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")


async def show_table_info(table_name: str):
    """显示表结构信息"""
    print(f"表 '{table_name}' 的结构信息:")
    try:
        async with AsyncSessionLocal() as db:
            result = await db.execute(text(f"DESCRIBE {table_name}"))
            columns = result.fetchall()
            if columns:
                print("  字段名    | 类型      | 是否为空 | 键   | 默认值 | 额外")
                print("  " + "-" * 60)
                for col in columns:
                    print(f"  {col[0]:<10} | {col[1]:<9} | {col[2]:<8} | {col[3]:<4} | {col[4] or '':<6} | {col[5] or ''}")
            else:
                print("  表不存在或没有字段")
    except Exception as e:
        print(f"❌ 获取表信息失败: {str(e)}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据库管理工具")
    parser.add_argument("command", choices=[
        "create", "drop", "reset", "show", "check", "info"
    ], help="要执行的命令")
    parser.add_argument("--table", help="表名（用于info命令）")
    
    args = parser.parse_args()
    
    if args.command == "create":
        await create_tables()
    elif args.command == "drop":
        confirm = input("确定要删除所有表吗？这将丢失所有数据！(y/N): ")
        if confirm.lower() == 'y':
            await drop_tables()
        else:
            print("操作已取消")
    elif args.command == "reset":
        confirm = input("确定要重置所有表吗？这将丢失所有数据！(y/N): ")
        if confirm.lower() == 'y':
            await reset_tables()
        else:
            print("操作已取消")
    elif args.command == "show":
        await show_tables()
    elif args.command == "check":
        await check_connection()
    elif args.command == "info":
        if not args.table:
            print("请使用 --table 参数指定表名")
            return
        await show_table_info(args.table)


if __name__ == "__main__":
    asyncio.run(main())
