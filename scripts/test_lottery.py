#!/usr/bin/env python3
"""
抽奖功能测试脚本
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any


class LotteryTester:
    """抽奖功能测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        async with self.session.request(method, url, **kwargs) as response:
            try:
                data = await response.json()
            except:
                data = {"error": "Invalid JSON response"}
            
            return {
                "status": response.status,
                "data": data
            }
    
    async def test_create_lottery_code(self):
        """测试创建抽奖二维码"""
        print("🎯 测试创建抽奖二维码...")
        
        code_data = {
            "url": "https://example.com/qr/test123",
            "prize_level": 1
        }
        
        result = await self.request(
            "POST", 
            "/api/v1/lottery-codes/",
            json=code_data
        )
        
        print(f"  创建二维码: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 201:
            code_info = result['data'].get('data', {})
            print(f"  创建的二维码ID: {code_info.get('id')}")
            print(f"  二维码编码: {code_info.get('code')}")
            return code_info.get('code')
        
        return None
    
    async def test_batch_create_lottery_codes(self):
        """测试批量创建抽奖二维码"""
        print("\n🎯 测试批量创建抽奖二维码...")
        
        batch_data = {
            "count": 5,
            "url_template": "https://example.com/qr/{code}",
            "prize_levels": [0, 0, 0, 1, 2]  # 3个谢谢参与，1个一等奖，1个二等奖
        }
        
        result = await self.request(
            "POST",
            "/api/v1/lottery-codes/batch",
            json=batch_data
        )
        
        print(f"  批量创建: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 201:
            codes_info = result['data'].get('data', {})
            codes = codes_info.get('codes', [])
            print(f"  创建数量: {len(codes)}")
            return [code['code'] for code in codes]
        
        return []
    
    async def test_get_lottery_codes(self):
        """测试获取抽奖二维码列表"""
        print("\n📋 测试获取抽奖二维码列表...")
        
        result = await self.request("GET", "/api/v1/lottery-codes/?page=1&size=10")
        print(f"  获取列表: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 200:
            list_data = result['data'].get('data', {})
            print(f"  总数量: {list_data.get('total', 0)}")
            print(f"  当前页数量: {len(list_data.get('codes', []))}")
            
            # 返回第一个未使用的二维码编码
            codes = list_data.get('codes', [])
            for code in codes:
                if not code.get('is_used', True):
                    return code.get('code')
        
        return None
    
    async def test_scan_lottery_code(self, code: str):
        """测试扫码抽奖"""
        print(f"\n🎲 测试扫码抽奖 (编码: {code[:8]}...)...")
        
        scan_data = {
            "code": code
        }
        
        result = await self.request(
            "POST",
            "/api/v1/lottery-codes/scan",
            json=scan_data
        )
        
        print(f"  扫码结果: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 200:
            scan_result = result['data'].get('data', {})
            print(f"  是否成功: {scan_result.get('success', False)}")
            print(f"  奖品等级: {scan_result.get('prize_level', 'N/A')}")
            print(f"  奖品名称: {scan_result.get('prize_name', 'N/A')}")
            return True
        else:
            scan_result = result['data'].get('data', {})
            print(f"  失败原因: {scan_result.get('message', '未知错误')}")
            return False
    
    async def test_get_lottery_stats(self):
        """测试获取抽奖统计"""
        print("\n📊 测试获取抽奖统计...")
        
        result = await self.request("GET", "/api/v1/lottery-codes/stats/overview")
        print(f"  获取统计: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 200:
            stats = result['data'].get('data', {})
            print(f"  总二维码数: {stats.get('total_codes', 0)}")
            print(f"  已使用数: {stats.get('used_codes', 0)}")
            print(f"  未使用数: {stats.get('unused_codes', 0)}")
            
            prize_stats = stats.get('prize_stats', {})
            if prize_stats:
                print("  奖品等级分布:")
                for level, count in prize_stats.items():
                    prize_name = self.get_prize_name(int(level))
                    print(f"    {prize_name}: {count}个")
    
    def get_prize_name(self, prize_level: int) -> str:
        """获取奖品名称"""
        prize_names = {
            0: "谢谢参与",
            1: "一等奖",
            2: "二等奖", 
            3: "三等奖",
            4: "四等奖",
            5: "五等奖"
        }
        return prize_names.get(prize_level, f"第{prize_level}等奖")
    
    async def test_get_code_by_code(self, code: str):
        """测试根据编码获取二维码信息"""
        print(f"\n🔍 测试根据编码获取信息 (编码: {code[:8]}...)...")
        
        result = await self.request("GET", f"/api/v1/lottery-codes/code/{code}")
        print(f"  获取信息: {result['status']} - {result['data'].get('message', '')}")
        
        if result['status'] == 200:
            code_info = result['data'].get('data', {})
            print(f"  二维码ID: {code_info.get('id')}")
            print(f"  奖品等级: {code_info.get('prize_level')}")
            print(f"  是否已使用: {code_info.get('is_used')}")
            print(f"  扫码时间: {code_info.get('scan_time', '未扫码')}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始抽奖功能测试...")
        print("=" * 60)
        
        try:
            # 1. 创建单个二维码
            single_code = await self.test_create_lottery_code()
            
            # 2. 批量创建二维码
            batch_codes = await self.test_batch_create_lottery_codes()
            
            # 3. 获取二维码列表
            list_code = await self.test_get_lottery_codes()
            
            # 4. 选择一个二维码进行测试
            test_code = single_code or (batch_codes[0] if batch_codes else list_code)
            
            if test_code:
                # 5. 根据编码获取信息
                await self.test_get_code_by_code(test_code)
                
                # 6. 扫码抽奖
                await self.test_scan_lottery_code(test_code)
                
                # 7. 再次扫码（应该失败）
                print(f"\n🎲 测试重复扫码 (编码: {test_code[:8]}...)...")
                await self.test_scan_lottery_code(test_code)
            
            # 8. 获取统计信息
            await self.test_get_lottery_stats()
            
            print("\n" + "=" * 60)
            print("✅ 所有抽奖功能测试完成！")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {str(e)}")


async def main():
    """主函数"""
    print("🎰 抽奖系统功能测试")
    print("请确保服务已启动: http://localhost:8000")
    
    input("按回车键开始测试...")
    
    async with LotteryTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
