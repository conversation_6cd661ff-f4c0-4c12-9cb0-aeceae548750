#!/usr/bin/env python3
"""
批量生成抽奖二维码脚本
基于Augment Context Engine检索的现有代码架构实现
支持灵活的奖品分配策略和导出功能
"""
import asyncio
import os
import sys
import argparse
import json
import csv
from typing import List, Dict, Optional
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.config.database import AsyncSessionLocal
from app.services.lottery_code_service import LotteryCodeService
from app.schemas.lottery_code import LotteryCodeBatchCreate
from app.utils.snowflake import generate_snowflake_id, parse_snowflake_id
from app.utils.logger import get_logger

logger = get_logger(__name__)


class LotteryCodeBatchGenerator:
    """抽奖二维码批量生成器
    
    基于现有的LotteryCodeService和数据库架构实现
    支持多种奖品分配策略和数据导出功能
    """
    
    def __init__(self):
        self.db_session = None
        self.generated_codes = []
        self.statistics = {
            "total_requested": 0,
            "total_generated": 0,
            "generation_time": 0,
            "prize_distribution": {},
            "start_time": None,
            "end_time": None,
            "success_rate": 0.0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.db_session = AsyncSessionLocal()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.db_session:
            await self.db_session.close()
    
    def calculate_prize_distribution(self, total_count: int, strategy: str = "default", **kwargs) -> List[int]:
        """计算奖品分配策略
        
        Args:
            total_count: 总生成数量
            strategy: 分配策略 (default, custom, percentage, fixed)
            **kwargs: 策略参数
        
        Returns:
            List[int]: 奖品等级列表
        """
        prize_levels = []
        
        if strategy == "default":
            # 默认策略：90%谢谢参与，8%三等奖，1.5%二等奖，0.5%一等奖
            no_prize = int(total_count * 0.90)
            third_prize = int(total_count * 0.08)
            second_prize = int(total_count * 0.015)
            first_prize = total_count - no_prize - third_prize - second_prize
            
            prize_levels = ([0] * no_prize + [3] * third_prize + 
                          [2] * second_prize + [1] * first_prize)
        
        elif strategy == "percentage":
            # 百分比策略
            percentages = kwargs.get("percentages", {0: 0.9, 1: 0.1})
            for level, percentage in percentages.items():
                count = int(total_count * percentage)
                prize_levels.extend([level] * count)
        
        elif strategy == "fixed":
            # 固定数量策略
            fixed_counts = kwargs.get("fixed_counts", {0: total_count})
            for level, count in fixed_counts.items():
                prize_levels.extend([level] * count)
        
        elif strategy == "custom":
            # 自定义列表
            prize_levels = kwargs.get("prize_levels", [0] * total_count)
        
        # 确保数量匹配
        while len(prize_levels) < total_count:
            prize_levels.append(0)  # 用谢谢参与补齐
        
        if len(prize_levels) > total_count:
            prize_levels = prize_levels[:total_count]  # 截取多余部分
        
        return prize_levels
    
    def generate_url_template(self, base_url: str = None, template: str = None) -> str:
        """生成URL模板
        
        Args:
            base_url: 基础URL
            template: 自定义模板
        
        Returns:
            str: URL模板字符串
        """
        if template:
            if "{code}" not in template:
                raise ValueError("URL模板必须包含{code}占位符")
            return template
        
        if not base_url:
            base_url = "https://example.com/lottery"
        
        if not base_url.endswith("/"):
            base_url += "/"
        
        return f"{base_url}{{code}}"
    
    async def generate_batch(self, count: int, **config) -> List[Dict]:
        """批量生成抽奖二维码
        
        Args:
            count: 生成数量
            **config: 配置参数
        
        Returns:
            List[Dict]: 生成的二维码信息列表
        """
        self.statistics["start_time"] = datetime.now()
        self.statistics["total_requested"] = count
        
        print(f"🎯 开始批量生成 {count} 个抽奖二维码...")
        
        # 计算奖品分配
        strategy = config.get("strategy", "default")
        prize_levels = self.calculate_prize_distribution(count, strategy, **config)
        
        # 生成URL模板
        url_template = self.generate_url_template(
            config.get("base_url"), 
            config.get("url_template")
        )
        
        # 显示奖品分配统计
        self._print_prize_distribution(prize_levels, count)
        
        # 创建批量数据对象
        batch_data = LotteryCodeBatchCreate(
            count=count,
            url_template=url_template,
            prize_levels=prize_levels
        )
        
        try:
            # 使用现有的LotteryCodeService批量创建
            lottery_codes = await LotteryCodeService.batch_create_lottery_codes(
                self.db_session, batch_data
            )
            
            # 转换为详细信息字典
            self.generated_codes = []
            for code in lottery_codes:
                # 解析雪花ID获取详细信息
                snowflake_info = parse_snowflake_id(code.id)
                
                code_dict = {
                    "id": code.id,
                    "code": code.code,
                    "url": code.url,
                    "prize_level": code.prize_level,
                    "prize_name": LotteryCodeService.get_prize_name(code.prize_level),
                    "is_used": code.is_used,
                    "create_at": code.create_at.isoformat() if code.create_at else None,
                    "snowflake_info": snowflake_info,
                    "qr_code_url": f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={code.url}"
                }
                self.generated_codes.append(code_dict)
            
            # 更新统计信息
            self._update_statistics()
            
            print(f"✅ 批量生成完成！")
            print(f"   实际生成数量: {self.statistics['total_generated']}")
            print(f"   生成耗时: {self.statistics['generation_time']:.2f}秒")
            print(f"   平均速度: {self.statistics['total_generated']/self.statistics['generation_time']:.1f}个/秒")
            print(f"   成功率: {self.statistics['success_rate']:.1f}%")
            
            return self.generated_codes
            
        except Exception as e:
            logger.error(f"批量生成失败: {str(e)}")
            raise
    
    def _print_prize_distribution(self, prize_levels: List[int], total_count: int):
        """打印奖品分配信息"""
        print(f"📋 奖品分配策略:")
        prize_stats = {}
        for level in prize_levels:
            prize_stats[level] = prize_stats.get(level, 0) + 1
        
        for level in sorted(prize_stats.keys()):
            count = prize_stats[level]
            prize_name = LotteryCodeService.get_prize_name(level)
            percentage = (count / total_count) * 100
            print(f"   {prize_name}: {count}个 ({percentage:.1f}%)")
    
    def _update_statistics(self):
        """更新统计信息"""
        self.statistics["end_time"] = datetime.now()
        self.statistics["total_generated"] = len(self.generated_codes)
        self.statistics["generation_time"] = (
            self.statistics["end_time"] - self.statistics["start_time"]
        ).total_seconds()
        
        # 计算奖品分布
        prize_distribution = {}
        for code in self.generated_codes:
            level = code["prize_level"]
            prize_distribution[level] = prize_distribution.get(level, 0) + 1
        
        self.statistics["prize_distribution"] = prize_distribution
        self.statistics["success_rate"] = (
            self.statistics["total_generated"] / self.statistics["total_requested"] * 100
            if self.statistics["total_requested"] > 0 else 0
        )
    
    async def export_to_csv(self, filename: str = None):
        """导出到CSV文件"""
        if not self.generated_codes:
            print("❌ 没有可导出的数据")
            return
        
        if not filename:
            filename = f"lottery_codes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        print(f"📄 导出数据到CSV文件: {filename}")
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'id', 'code', 'url', 'prize_level', 'prize_name', 
                'is_used', 'create_at', 'qr_code_url', 'generation_time'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for code_data in self.generated_codes:
                row = {
                    'id': code_data['id'],
                    'code': code_data['code'],
                    'url': code_data['url'],
                    'prize_level': code_data['prize_level'],
                    'prize_name': code_data['prize_name'],
                    'is_used': code_data['is_used'],
                    'create_at': code_data['create_at'],
                    'qr_code_url': code_data['qr_code_url'],
                    'generation_time': code_data['snowflake_info']['datetime']
                }
                writer.writerow(row)
        
        print(f"✅ CSV导出完成: {filename}")
        return filename
    
    async def export_to_json(self, filename: str = None):
        """导出到JSON文件"""
        if not self.generated_codes:
            print("❌ 没有可导出的数据")
            return
        
        if not filename:
            filename = f"lottery_codes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        print(f"📄 导出数据到JSON文件: {filename}")
        
        export_data = {
            "metadata": {
                "export_time": datetime.now().isoformat(),
                "total_codes": len(self.generated_codes),
                "statistics": self.statistics,
                "generator_info": {
                    "script_version": "1.0.0",
                    "based_on": "LotteryCodeService",
                    "snowflake_generator": "enabled"
                }
            },
            "codes": self.generated_codes
        }
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON导出完成: {filename}")
        return filename
    
    def print_detailed_statistics(self):
        """打印详细统计信息"""
        print("\n📊 详细统计信息:")
        print(f"   请求生成数量: {self.statistics['total_requested']}")
        print(f"   实际生成数量: {self.statistics['total_generated']}")
        print(f"   成功率: {self.statistics['success_rate']:.1f}%")
        print(f"   生成耗时: {self.statistics['generation_time']:.2f}秒")
        
        if self.statistics['generation_time'] > 0:
            speed = self.statistics['total_generated'] / self.statistics['generation_time']
            print(f"   生成速度: {speed:.1f}个/秒")
        
        print(f"   开始时间: {self.statistics['start_time']}")
        print(f"   结束时间: {self.statistics['end_time']}")
        
        print("\n🎁 奖品分布详情:")
        total = self.statistics['total_generated']
        for level in sorted(self.statistics['prize_distribution'].keys()):
            count = self.statistics['prize_distribution'][level]
            prize_name = LotteryCodeService.get_prize_name(level)
            percentage = (count / total) * 100 if total > 0 else 0
            print(f"   {prize_name} (等级{level}): {count}个 ({percentage:.1f}%)")


def parse_strategy_config(strategy_str: str) -> Dict:
    """解析策略配置字符串"""
    if not strategy_str:
        return {"strategy": "default"}
    
    try:
        # 尝试解析JSON格式
        if strategy_str.startswith('{'):
            return json.loads(strategy_str)
        
        # 解析简单格式: strategy:param=value,param2=value2
        parts = strategy_str.split(':')
        strategy = parts[0]
        config = {"strategy": strategy}
        
        if len(parts) > 1:
            params = parts[1].split(',')
            for param in params:
                if '=' in param:
                    key, value = param.split('=', 1)
                    try:
                        # 尝试转换为数字
                        config[key] = float(value) if '.' in value else int(value)
                    except ValueError:
                        config[key] = value
        
        return config
    except Exception as e:
        print(f"⚠️  策略配置解析失败: {e}")
        return {"strategy": "default"}


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="批量生成抽奖二维码",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python scripts/batch_generate_lottery_codes.py 1000
  python scripts/batch_generate_lottery_codes.py 500 --strategy "percentage" --base-url "https://mysite.com/lottery"
  python scripts/batch_generate_lottery_codes.py 100 --export-csv codes.csv --export-json codes.json
        """
    )
    
    parser.add_argument("count", type=int, help="生成数量")
    parser.add_argument("--strategy", help="奖品分配策略 (default/percentage/fixed/custom)")
    parser.add_argument("--base-url", help="基础URL")
    parser.add_argument("--url-template", help="URL模板 (必须包含{code})")
    parser.add_argument("--export-csv", help="导出CSV文件名")
    parser.add_argument("--export-json", help="导出JSON文件名")
    parser.add_argument("--config", help="策略配置 (JSON格式或简单格式)")
    
    args = parser.parse_args()
    
    # 验证参数
    if args.count <= 0:
        print("❌ 生成数量必须大于0")
        return
    
    if args.count > 50000:
        confirm = input(f"⚠️  您要生成 {args.count} 个二维码，这可能需要较长时间。确认继续？(y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
    
    # 解析配置
    config = parse_strategy_config(args.config) if args.config else {}
    
    # 命令行参数覆盖配置
    if args.strategy:
        config["strategy"] = args.strategy
    if args.base_url:
        config["base_url"] = args.base_url
    if args.url_template:
        config["url_template"] = args.url_template
    
    print("🎰 抽奖二维码批量生成器")
    print("=" * 60)
    print(f"生成数量: {args.count}")
    print(f"配置策略: {config.get('strategy', 'default')}")
    print(f"基础URL: {config.get('base_url', 'https://example.com/lottery')}")
    print("=" * 60)
    
    try:
        async with LotteryCodeBatchGenerator() as generator:
            # 批量生成
            codes = await generator.generate_batch(args.count, **config)
            
            # 打印详细统计
            generator.print_detailed_statistics()
            
            # 导出文件
            exported_files = []
            if args.export_csv:
                csv_file = await generator.export_to_csv(args.export_csv)
                exported_files.append(csv_file)
            
            if args.export_json:
                json_file = await generator.export_to_json(args.export_json)
                exported_files.append(json_file)
            
            # 如果没有指定导出文件，询问是否导出
            if not args.export_csv and not args.export_json:
                export_choice = input("\n是否导出生成的数据？(csv/json/both/n): ").lower()
                if export_choice in ['csv', 'both']:
                    csv_file = await generator.export_to_csv()
                    exported_files.append(csv_file)
                if export_choice in ['json', 'both']:
                    json_file = await generator.export_to_json()
                    exported_files.append(json_file)
            
            print(f"\n🎉 批量生成完成！")
            print(f"   共生成 {len(codes)} 个抽奖二维码")
            if exported_files:
                print(f"   导出文件: {', '.join(exported_files)}")
            
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {str(e)}")
        logger.error(f"批量生成失败: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
