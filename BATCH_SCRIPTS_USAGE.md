# 抽奖二维码批量操作脚本使用指南

基于Augment Context Engine检索的现有代码架构，我为您创建了两个功能强大的脚本：

## 📋 脚本概览

### 1. 批量生成脚本 (`batch_generate_lottery_codes.py`)
- **功能**: 批量生成抽奖二维码
- **特点**: 支持多种奖品分配策略、雪花ID生成、数据导出
- **基于**: `LotteryCodeService.batch_create_lottery_codes()` 方法

### 2. 奖品等级赋值脚本 (`assign_prize_levels.py`)
- **功能**: 为现有二维码分配奖品等级
- **特点**: 支持多种赋值策略、条件筛选、批量更新
- **基于**: `LotteryCodeService.update_lottery_code()` 方法和SQL批量更新

## 🚀 脚本1：批量生成抽奖二维码

### 基本用法

```bash
# 生成1000个二维码（使用默认策略）
python scripts/batch_generate_lottery_codes.py 1000

# 生成500个二维码并导出到CSV
python scripts/batch_generate_lottery_codes.py 500 --export-csv codes.csv

# 使用自定义URL模板
python scripts/batch_generate_lottery_codes.py 100 --url-template "https://mysite.com/lottery/{code}"
```

### 奖品分配策略

#### 1. 默认策略 (default)
```bash
python scripts/batch_generate_lottery_codes.py 1000 --strategy default
```
- 90% 谢谢参与 (等级0)
- 8% 三等奖 (等级3)
- 1.5% 二等奖 (等级2)
- 0.5% 一等奖 (等级1)

#### 2. 百分比策略 (percentage)
```bash
python scripts/batch_generate_lottery_codes.py 1000 --strategy percentage --config '{"percentages": {"0": 0.85, "1": 0.05, "2": 0.1}}'
```

#### 3. 固定数量策略 (fixed)
```bash
python scripts/batch_generate_lottery_codes.py 1000 --strategy fixed --config '{"fixed_counts": {"0": 900, "1": 50, "2": 50}}'
```

#### 4. 自定义策略 (custom)
```bash
python scripts/batch_generate_lottery_codes.py 100 --strategy custom --config '{"prize_levels": [0,0,0,1,2,0,0,0,0,3]}'
```

### 高级功能

#### 导出功能
```bash
# 同时导出CSV和JSON
python scripts/batch_generate_lottery_codes.py 1000 --export-csv codes.csv --export-json codes.json

# 自动生成文件名
python scripts/batch_generate_lottery_codes.py 1000
# 运行后会询问是否导出
```

#### 自定义URL
```bash
# 使用基础URL
python scripts/batch_generate_lottery_codes.py 1000 --base-url "https://mycompany.com/lottery"

# 使用完整模板
python scripts/batch_generate_lottery_codes.py 1000 --url-template "https://api.mysite.com/scan?code={code}&activity=spring2024"
```

### 输出示例

```
🎰 抽奖二维码批量生成器
============================================================
生成数量: 1000
配置策略: default
基础URL: https://example.com/lottery
============================================================
🎯 开始批量生成 1000 个抽奖二维码...
📋 奖品分配策略:
   谢谢参与: 900个 (90.0%)
   三等奖: 80个 (8.0%)
   二等奖: 15个 (1.5%)
   一等奖: 5个 (0.5%)
✅ 批量生成完成！
   实际生成数量: 1000
   生成耗时: 2.34秒
   平均速度: 427.4个/秒
   成功率: 100.0%
```

## 🎯 脚本2：奖品等级赋值

### 基本用法

```bash
# 随机选择100个二维码设为一等奖
python scripts/assign_prize_levels.py 1 --strategy random --count 100

# 将所有未使用的二维码中的10%设为二等奖
python scripts/assign_prize_levels.py 2 --strategy percentage --percentage 0.1 --criteria "is_used=false"

# 指定ID设为特等奖
python scripts/assign_prize_levels.py 10 --strategy specific_ids --ids "123456,789012,345678"
```

### 分配策略详解

#### 1. 随机策略 (random)
```bash
python scripts/assign_prize_levels.py 1 --strategy random --count 50 --criteria "is_used=false"
```
- 从符合条件的二维码中随机选择指定数量
- 适用于公平分配奖品

#### 2. 顺序策略 (sequential)
```bash
python scripts/assign_prize_levels.py 2 --strategy sequential --count 30 --criteria "current_prize_level=0"
```
- 按创建时间顺序选择
- 适用于先到先得的场景

#### 3. 百分比策略 (percentage)
```bash
python scripts/assign_prize_levels.py 1 --strategy percentage --percentage 0.05 --criteria "is_used=false"
```
- 按百分比选择符合条件的二维码
- 适用于按比例分配奖品

#### 4. 指定ID策略 (specific_ids)
```bash
python scripts/assign_prize_levels.py 1 --strategy specific_ids --ids "1234567890,2345678901,3456789012"
```
- 精确指定要更新的二维码ID
- 适用于特定二维码的奖品设置

#### 5. 指定编码策略 (specific_codes)
```bash
python scripts/assign_prize_levels.py 1 --strategy specific_codes --codes "abc123def456,def456ghi789"
```
- 通过32位编码指定二维码
- 适用于通过编码识别的场景

#### 6. 批量更新策略 (batch_update)
```bash
python scripts/assign_prize_levels.py 0 --strategy batch_update --criteria "current_prize_level=1,is_used=true"
```
- 使用SQL批量更新，性能最佳
- 适用于大量数据的快速更新

### 筛选条件

#### 基本条件
```bash
# 只处理未使用的二维码
--criteria "is_used=false"

# 只处理特定奖品等级的二维码
--criteria "current_prize_level=0"

# 组合条件
--criteria "is_used=false,current_prize_level=0"
```

#### 高级条件
```bash
# ID范围筛选
--criteria "id_range=1000:2000"

# 多个奖品等级
--criteria "current_prize_level=[0,1,2]"

# JSON格式条件
--criteria '{"is_used": false, "current_prize_level": [0, 1], "limit": 1000}'
```

### 预览功能

```bash
# 预览分配结果，不实际执行
python scripts/assign_prize_levels.py 1 --strategy random --count 100 --preview
```

输出示例：
```
📋 分配预览:
   符合条件的总数: 5000
   将要更新的数量: 100
   当前奖品分布: {0: 4500, 1: 300, 2: 150, 3: 50}
```

### 日志导出

```bash
# 指定日志文件名
python scripts/assign_prize_levels.py 1 --strategy random --count 100 --export-log assignment.csv

# 自动生成日志文件名
python scripts/assign_prize_levels.py 1 --strategy random --count 100
# 执行后会询问是否导出日志
```

## 🔧 技术特性

### 基于现有架构
两个脚本都充分利用了Augment Context Engine检索到的现有代码：

1. **数据库连接**: 使用 `AsyncSessionLocal` 和 `get_database()`
2. **服务层**: 基于 `LotteryCodeService` 的方法
3. **雪花ID**: 使用 `generate_snowflake_id()` 和 `parse_snowflake_id()`
4. **数据模型**: 使用 `LotteryCode` 模型和 `LotteryCodeBatchCreate` 等Schema
5. **日志系统**: 使用 `get_logger()` 进行日志记录

### 性能优化
- **批量操作**: 使用数据库批量插入和更新
- **异步处理**: 基于asyncio的异步数据库操作
- **内存优化**: 流式处理大量数据
- **SQL优化**: 使用索引和批量SQL语句

### 数据安全
- **事务支持**: 所有操作都在数据库事务中执行
- **回滚机制**: 出错时自动回滚
- **预览功能**: 执行前可预览结果
- **日志记录**: 详细记录所有操作

## 📊 实际应用场景

### 场景1：新活动准备
```bash
# 1. 生成10000个二维码，90%谢谢参与，10%中奖
python scripts/batch_generate_lottery_codes.py 10000 --strategy percentage \
  --config '{"percentages": {"0": 0.9, "1": 0.05, "2": 0.03, "3": 0.02}}' \
  --base-url "https://mycompany.com/spring2024" \
  --export-csv spring2024_codes.csv

# 2. 导出的CSV可用于打印二维码
```

### 场景2：活动中调整
```bash
# 1. 预览当前未使用的二维码分布
python scripts/assign_prize_levels.py 1 --strategy percentage --percentage 0.02 \
  --criteria "is_used=false" --preview

# 2. 将2%的未使用二维码设为一等奖
python scripts/assign_prize_levels.py 1 --strategy percentage --percentage 0.02 \
  --criteria "is_used=false" --export-log prize_adjustment.csv
```

### 场景3：特殊奖品设置
```bash
# 为VIP客户的特定二维码设置特等奖
python scripts/assign_prize_levels.py 10 --strategy specific_codes \
  --codes "vip001code123,vip002code456,vip003code789"
```

### 场景4：活动结束清理
```bash
# 将所有已使用的中奖二维码重置为谢谢参与（用于数据分析）
python scripts/assign_prize_levels.py 0 --strategy batch_update \
  --criteria "is_used=true,current_prize_level=[1,2,3]" --preview
```

## 🎉 总结

这两个脚本提供了完整的抽奖二维码管理解决方案：

1. **生成阶段**: 使用 `batch_generate_lottery_codes.py` 批量创建二维码
2. **管理阶段**: 使用 `assign_prize_levels.py` 灵活调整奖品分配
3. **数据导出**: 支持CSV和JSON格式的数据导出
4. **操作日志**: 详细记录所有操作，便于审计和回溯

两个脚本都基于现有的代码架构，确保了与系统的完美集成和高性能表现。
