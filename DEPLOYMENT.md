# 部署指南

## 快速开始

### 1. 本地开发环境

```bash
# 1. 克隆项目
git clone <repository-url>
cd activity-lottery

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件设置数据库连接等

# 5. 启动开发服务器
python scripts/start_dev.py
```

### 2. Docker开发环境

```bash
# 1. 启动所有服务（包括MySQL）
docker-compose up -d

# 2. 查看服务状态
docker-compose ps

# 3. 查看日志
docker-compose logs -f app

# 4. 停止服务
docker-compose down
```

## 生产环境部署

### 方式一：Docker部署（推荐）

#### 1. 准备环境

```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 配置生产环境

```bash
# 1. 创建生产环境配置
cp .env.example .env.prod

# 2. 编辑生产环境变量
vim .env.prod
```

生产环境变量示例：
```bash
ENVIRONMENT=prd
DB_HOST=mysql
DB_PORT=3306
DB_USERNAME=app_user
DB_PASSWORD=your_secure_password
DB_NAME=activity_lottery
SECRET_KEY=your-super-secure-secret-key
```

#### 3. 创建生产Docker Compose文件

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: activity-lottery-mysql-prod
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_prod_data:/var/lib/mysql
    restart: unless-stopped
    networks:
      - app-network

  app:
    build: .
    container_name: activity-lottery-app-prod
    env_file:
      - .env.prod
    ports:
      - "80:8000"
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - app-network

volumes:
  mysql_prod_data:

networks:
  app-network:
    driver: bridge
```

#### 4. 部署应用

```bash
# 1. 构建并启动服务
docker-compose -f docker-compose.prod.yml up -d

# 2. 初始化数据库
docker-compose -f docker-compose.prod.yml exec app python scripts/db_manager.py create

# 3. 创建超级用户
docker-compose -f docker-compose.prod.yml exec app python scripts/create_superuser.py

# 4. 检查服务状态
curl http://localhost/api/v1/health/ping
```

### 方式二：直接部署

#### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python 3.11
sudo apt install python3.11 python3.11-venv python3.11-dev

# 安装MySQL
sudo apt install mysql-server

# 安装Nginx（可选，用于反向代理）
sudo apt install nginx
```

#### 2. 应用部署

```bash
# 1. 创建应用用户
sudo useradd -m -s /bin/bash appuser

# 2. 切换到应用用户
sudo su - appuser

# 3. 克隆代码
git clone <repository-url> /home/<USER>/activity-lottery
cd /home/<USER>/activity-lottery

# 4. 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 5. 安装依赖
pip install -r requirements.txt

# 6. 配置环境
cp .env.example .env
# 编辑 .env 文件

# 7. 初始化数据库
python scripts/db_manager.py create
python scripts/create_superuser.py
```

#### 3. 系统服务配置

创建systemd服务文件：

```bash
sudo vim /etc/systemd/system/activity-lottery.service
```

```ini
[Unit]
Description=Activity Lottery API
After=network.target mysql.service

[Service]
Type=simple
User=appuser
Group=appuser
WorkingDirectory=/home/<USER>/activity-lottery
Environment=PATH=/home/<USER>/activity-lottery/venv/bin
ExecStart=/home/<USER>/activity-lottery/venv/bin/python scripts/start_prd.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start activity-lottery

# 设置开机自启
sudo systemctl enable activity-lottery

# 查看服务状态
sudo systemctl status activity-lottery
```

#### 4. Nginx反向代理（可选）

```bash
sudo vim /etc/nginx/sites-available/activity-lottery
```

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/activity-lottery /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 监控和维护

### 1. 日志管理

```bash
# 查看应用日志
tail -f logs/app_prd.log

# 查看系统服务日志
sudo journalctl -u activity-lottery -f

# 日志轮转配置
sudo vim /etc/logrotate.d/activity-lottery
```

### 2. 数据库备份

```bash
# 创建备份脚本
vim backup.sh
```

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
DB_NAME="activity_lottery"

mkdir -p $BACKUP_DIR

mysqldump -u root -p $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 保留最近7天的备份
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
```

### 3. 健康检查

```bash
# 创建健康检查脚本
vim health_check.sh
```

```bash
#!/bin/bash
HEALTH_URL="http://localhost:8000/api/v1/health/ping"

if curl -f $HEALTH_URL > /dev/null 2>&1; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy"
    exit 1
fi
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接配置
   - 检查防火墙设置

2. **服务启动失败**
   - 查看日志文件
   - 检查端口占用
   - 验证环境变量

3. **性能问题**
   - 检查数据库连接池配置
   - 监控系统资源使用
   - 优化数据库查询

### 日志分析

```bash
# 查看错误日志
grep "ERROR" logs/app_prd.log

# 查看访问统计
grep "INFO" logs/app_prd.log | grep "请求完成" | wc -l

# 查看慢请求
grep "处理时间" logs/app_prd.log | awk '$NF > 1.0'
```

## 安全建议

1. **定期更新依赖**
2. **使用强密码**
3. **配置防火墙**
4. **启用HTTPS**
5. **定期备份数据**
6. **监控异常访问**

## 扩展部署

### 负载均衡

使用Nginx进行负载均衡：

```nginx
upstream app_servers {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://app_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 数据库集群

配置MySQL主从复制或使用云数据库服务。

### 缓存层

集成Redis缓存以提高性能。
