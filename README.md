# Activity Lottery API

活动抽奖系统后端API，基于FastAPI + SQLAlchemy + MySQL构建。

## 项目特性

- 🚀 **FastAPI**: 高性能异步Web框架
- 🗄️ **SQLAlchemy**: 异步ORM，支持MySQL
- 🔐 **JWT认证**: 完整的用户认证和授权系统
- 👤 **用户管理**: 用户注册、登录、信息管理
- ⚙️ **配置管理**: 支持多环境配置(dev/prd)
- 📝 **日志系统**: 完整的日志配置和轮转
- 🛡️ **异常处理**: 统一的异常处理和响应格式
- 🔒 **安全中间件**: CORS、安全头、密码加密等
- 📊 **健康检查**: 服务和数据库健康检查接口
- 🐳 **容器化**: 支持Docker部署
- 🧪 **测试工具**: 内置API测试脚本
- 🛠️ **管理工具**: 数据库管理和超级用户创建脚本

## 项目结构

```
activity-lottery/
├── app/                    # 应用主目录
│   ├── config/            # 配置模块
│   │   ├── settings.py    # 配置管理
│   │   ├── database.py    # 数据库配置
│   │   └── logging.py     # 日志配置
│   ├── core/              # 核心模块
│   │   ├── response.py    # 统一响应格式
│   │   ├── exceptions.py  # 异常处理
│   │   ├── middleware.py  # 中间件
│   │   └── security.py    # 安全认证
│   ├── models/            # 数据模型
│   │   ├── base.py       # 模型基类
│   │   └── user.py       # 用户模型
│   ├── schemas/           # Pydantic模型
│   │   └── user.py       # 用户数据模型
│   ├── services/          # 业务逻辑层
│   │   └── user_service.py # 用户服务
│   ├── api/               # API路由
│   │   ├── deps.py       # 依赖注入
│   │   └── v1/           # API v1版本
│   │       └── endpoints/ # API端点
│   │           ├── health.py # 健康检查
│   │           ├── auth.py   # 认证接口
│   │           └── users.py  # 用户接口
│   ├── utils/             # 工具模块
│   └── main.py           # 应用入口
├── configs/               # 配置文件
│   ├── dev.yaml          # 开发环境配置
│   └── prd.yaml          # 生产环境配置
├── logs/                  # 日志目录
├── scripts/               # 脚本目录
│   ├── start_dev.py      # 开发启动脚本
│   ├── start_prd.py      # 生产启动脚本
│   ├── create_superuser.py # 创建超级用户
│   ├── db_manager.py     # 数据库管理
│   ├── test_api.py       # API测试脚本
│   └── init.sql          # 数据库初始化
├── requirements.txt       # 依赖文件
├── Dockerfile            # Docker镜像构建
├── docker-compose.yml    # Docker编排
├── .gitignore           # Git忽略文件
└── .dockerignore        # Docker忽略文件
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd activity-lottery

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，设置数据库连接等配置
```

### 3. 数据库准备

确保MySQL服务运行，并创建对应的数据库：

```sql
-- 开发环境
CREATE DATABASE activity_lottery_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 生产环境
CREATE DATABASE activity_lottery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 启动服务

#### 开发环境

```bash
python scripts/start_dev.py
```

#### 生产环境

```bash
python scripts/start_prd.py
```

### 5. 访问服务

- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/api/v1/health
- 根路径: http://localhost:8000/

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| ENVIRONMENT | 环境类型 | dev |
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 3306 |
| DB_USERNAME | 数据库用户名 | root |
| DB_PASSWORD | 数据库密码 | - |
| DB_NAME | 数据库名称 | activity_lottery |
| SECRET_KEY | 安全密钥 | - |

### 配置文件

- `configs/dev.yaml`: 开发环境配置
- `configs/prd.yaml`: 生产环境配置

配置文件支持环境变量替换，格式：`${VAR_NAME:default_value}`

## API接口

### 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "success": true,
  "timestamp": "2024-01-01T00:00:00"
}
```

### 主要API接口

#### 健康检查
- `GET /api/v1/health/` - 基础健康检查
- `GET /api/v1/health/detailed` - 详细健康检查（包含数据库）
- `GET /api/v1/health/ping` - 简单ping接口

#### 用户认证
- `POST /api/v1/auth/login` - 用户登录（表单格式）
- `POST /api/v1/auth/login/json` - 用户登录（JSON格式）
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/logout` - 用户登出

#### 用户管理
- `POST /api/v1/users/` - 创建用户
- `GET /api/v1/users/{user_id}` - 获取用户详情
- `PUT /api/v1/users/{user_id}` - 更新用户信息
- `DELETE /api/v1/users/{user_id}` - 删除用户
- `GET /api/v1/users/` - 获取用户列表（分页）
- `GET /api/v1/users/username/{username}` - 根据用户名获取用户

## 开发指南

### 添加新的API接口

1. 在 `app/api/v1/endpoints/` 下创建新的路由文件
2. 在 `app/api/v1/__init__.py` 中注册路由
3. 使用统一的响应格式和异常处理

### 添加数据模型

1. 在 `app/models/` 下创建模型文件
2. 继承 `BaseModel` 类
3. 在需要的地方导入使用

### 日志使用

```python
from app.utils.logger import get_logger

logger = get_logger(__name__)
logger.info("这是一条信息日志")
```

## 管理工具

### 数据库管理

```bash
# 创建数据库表
python scripts/db_manager.py create

# 查看所有表
python scripts/db_manager.py show

# 查看表结构
python scripts/db_manager.py info --table users

# 检查数据库连接
python scripts/db_manager.py check

# 重置数据库（危险操作）
python scripts/db_manager.py reset
```

### 创建超级用户

```bash
python scripts/create_superuser.py
```

### API测试

```bash
# 安装测试依赖
pip install aiohttp

# 运行API测试
python scripts/test_api.py
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t activity-lottery .

# 运行容器
docker run -d \
  --name activity-lottery \
  -p 8000:8000 \
  -e ENVIRONMENT=prd \
  -e DB_HOST=your-db-host \
  -e DB_PASSWORD=your-db-password \
  activity-lottery
```

### 系统服务部署

可以使用systemd、supervisor等工具管理服务进程。

## 许可证

MIT License
