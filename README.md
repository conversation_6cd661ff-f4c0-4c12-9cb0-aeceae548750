# Activity Lottery API

活动抽奖系统后端API，基于FastAPI + SQLAlchemy + MySQL构建。

## 项目特性

- 🚀 **FastAPI**: 高性能异步Web框架
- 🗄️ **SQLAlchemy**: 异步ORM，支持MySQL
- ⚙️ **配置管理**: 支持多环境配置(dev/prd)
- 📝 **日志系统**: 完整的日志配置和轮转
- 🛡️ **异常处理**: 统一的异常处理和响应格式
- 🔒 **安全中间件**: CORS、安全头等
- 📊 **健康检查**: 服务和数据库健康检查接口
- 🐳 **容器化**: 支持Docker部署

## 项目结构

```
activity-lottery/
├── app/                    # 应用主目录
│   ├── config/            # 配置模块
│   │   ├── settings.py    # 配置管理
│   │   ├── database.py    # 数据库配置
│   │   └── logging.py     # 日志配置
│   ├── core/              # 核心模块
│   │   ├── response.py    # 统一响应格式
│   │   ├── exceptions.py  # 异常处理
│   │   └── middleware.py  # 中间件
│   ├── models/            # 数据模型
│   │   └── base.py       # 模型基类
│   ├── api/               # API路由
│   │   ├── deps.py       # 依赖注入
│   │   └── v1/           # API v1版本
│   ├── utils/             # 工具模块
│   └── main.py           # 应用入口
├── configs/               # 配置文件
│   ├── dev.yaml          # 开发环境配置
│   └── prd.yaml          # 生产环境配置
├── logs/                  # 日志目录
├── scripts/               # 启动脚本
│   ├── start_dev.py      # 开发启动脚本
│   └── start_prd.py      # 生产启动脚本
└── requirements.txt       # 依赖文件
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd activity-lottery

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，设置数据库连接等配置
```

### 3. 数据库准备

确保MySQL服务运行，并创建对应的数据库：

```sql
-- 开发环境
CREATE DATABASE activity_lottery_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 生产环境
CREATE DATABASE activity_lottery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 启动服务

#### 开发环境

```bash
python scripts/start_dev.py
```

#### 生产环境

```bash
python scripts/start_prd.py
```

### 5. 访问服务

- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/api/v1/health
- 根路径: http://localhost:8000/

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| ENVIRONMENT | 环境类型 | dev |
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 3306 |
| DB_USERNAME | 数据库用户名 | root |
| DB_PASSWORD | 数据库密码 | - |
| DB_NAME | 数据库名称 | activity_lottery |
| SECRET_KEY | 安全密钥 | - |

### 配置文件

- `configs/dev.yaml`: 开发环境配置
- `configs/prd.yaml`: 生产环境配置

配置文件支持环境变量替换，格式：`${VAR_NAME:default_value}`

## API接口

### 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "success": true,
  "timestamp": "2024-01-01T00:00:00"
}
```

### 健康检查接口

- `GET /api/v1/health/` - 基础健康检查
- `GET /api/v1/health/detailed` - 详细健康检查（包含数据库）
- `GET /api/v1/health/ping` - 简单ping接口

## 开发指南

### 添加新的API接口

1. 在 `app/api/v1/endpoints/` 下创建新的路由文件
2. 在 `app/api/v1/__init__.py` 中注册路由
3. 使用统一的响应格式和异常处理

### 添加数据模型

1. 在 `app/models/` 下创建模型文件
2. 继承 `BaseModel` 类
3. 在需要的地方导入使用

### 日志使用

```python
from app.utils.logger import get_logger

logger = get_logger(__name__)
logger.info("这是一条信息日志")
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t activity-lottery .

# 运行容器
docker run -d \
  --name activity-lottery \
  -p 8000:8000 \
  -e ENVIRONMENT=prd \
  -e DB_HOST=your-db-host \
  -e DB_PASSWORD=your-db-password \
  activity-lottery
```

### 系统服务部署

可以使用systemd、supervisor等工具管理服务进程。

## 许可证

MIT License
