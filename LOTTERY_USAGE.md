# 抽奖二维码功能使用指南

## 功能概述

抽奖二维码系统提供了完整的二维码生成、管理和扫码抽奖功能，支持：

- 🎯 **二维码生成**：单个创建和批量创建
- 🎲 **扫码抽奖**：用户扫码参与抽奖
- 📊 **统计分析**：实时查看抽奖数据
- 🛠️ **管理功能**：二维码的增删改查

## 数据库表结构

```sql
CREATE TABLE lottery_code (
    id BIGINT NOT NULL PRIMARY KEY COMMENT '雪花ID',
    code VARCHAR(32) NOT NULL UNIQUE COMMENT '唯一编码(UUID去除-)',
    url VARCHAR(255) NOT NULL COMMENT '二维码链接',
    prize_level INT DEFAULT 0 NOT NULL COMMENT '中奖等级',
    is_used TINYINT(1) DEFAULT 0 NOT NULL COMMENT '是否使用过',
    ip VARCHAR(32) NULL COMMENT 'IP地址',
    scan_time DATETIME NULL COMMENT '扫码时间',
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

## 快速开始

### 1. 初始化数据库表

```bash
# 创建抽奖二维码表
python scripts/create_lottery_table.py
```

### 2. 测试抽奖功能

```bash
# 运行抽奖功能测试
python scripts/test_lottery.py
```

## API接口详解

### 1. 创建二维码

#### 单个创建
```http
POST /api/v1/lottery-codes/
Content-Type: application/json

{
    "url": "https://example.com/qr/abc123",
    "prize_level": 1
}
```

#### 批量创建
```http
POST /api/v1/lottery-codes/batch
Content-Type: application/json

{
    "count": 100,
    "url_template": "https://example.com/qr/{code}",
    "prize_levels": [0, 0, 0, 1, 2]
}
```

### 2. 扫码抽奖

```http
POST /api/v1/lottery-codes/scan
Content-Type: application/json

{
    "code": "a1b2c3d4e5f6789012345678901234ab"
}
```

响应示例：
```json
{
    "code": 200,
    "message": "恭喜中奖！",
    "data": {
        "success": true,
        "message": "恭喜中奖！",
        "prize_level": 1,
        "prize_name": "一等奖",
        "code": "a1b2c3d4e5f6789012345678901234ab"
    },
    "success": true,
    "timestamp": "2025-06-05T11:30:00.000000"
}
```

### 3. 查询二维码

#### 获取列表
```http
GET /api/v1/lottery-codes/?page=1&size=10&is_used=false&prize_level=1
```

#### 根据ID查询
```http
GET /api/v1/lottery-codes/123456789
```

#### 根据编码查询
```http
GET /api/v1/lottery-codes/code/a1b2c3d4e5f6789012345678901234ab
```

### 4. 统计信息

```http
GET /api/v1/lottery-codes/stats/overview
```

响应示例：
```json
{
    "code": 200,
    "data": {
        "total_codes": 1000,
        "used_codes": 150,
        "unused_codes": 850,
        "prize_stats": {
            "0": 800,
            "1": 100,
            "2": 80,
            "3": 20
        },
        "scan_stats": {
            "2025-06-05": 50,
            "2025-06-04": 100
        }
    }
}
```

## 奖品等级说明

| 等级 | 名称 | 说明 |
|------|------|------|
| 0 | 谢谢参与 | 未中奖 |
| 1 | 一等奖 | 最高奖项 |
| 2 | 二等奖 | 次高奖项 |
| 3 | 三等奖 | 第三等奖 |
| ... | ... | 可自定义更多等级 |

## 业务流程

### 1. 活动准备阶段

```python
# 1. 批量创建二维码
import requests

batch_data = {
    "count": 1000,
    "url_template": "https://yourdomain.com/lottery/{code}",
    "prize_levels": [0] * 900 + [1] * 50 + [2] * 30 + [3] * 20  # 奖品分配
}

response = requests.post(
    "http://localhost:8000/api/v1/lottery-codes/batch",
    json=batch_data
)
```

### 2. 用户参与阶段

```python
# 用户扫码抽奖
scan_data = {
    "code": "从二维码中获取的32位编码"
}

response = requests.post(
    "http://localhost:8000/api/v1/lottery-codes/scan",
    json=scan_data
)

result = response.json()
if result["data"]["success"]:
    prize_level = result["data"]["prize_level"]
    prize_name = result["data"]["prize_name"]
    print(f"恭喜获得：{prize_name}")
else:
    print(f"扫码失败：{result['data']['message']}")
```

### 3. 数据分析阶段

```python
# 获取统计数据
response = requests.get("http://localhost:8000/api/v1/lottery-codes/stats/overview")
stats = response.json()["data"]

print(f"总参与人数：{stats['used_codes']}")
print(f"中奖人数：{sum(int(count) for level, count in stats['prize_stats'].items() if int(level) > 0)}")
```

## 高级功能

### 1. 雪花ID生成

系统使用雪花算法生成唯一ID，确保分布式环境下的ID唯一性：

```python
from app.utils.snowflake import generate_snowflake_id, parse_snowflake_id

# 生成雪花ID
snowflake_id = generate_snowflake_id()

# 解析雪花ID
info = parse_snowflake_id(snowflake_id)
print(f"生成时间：{info['datetime']}")
```

### 2. UUID编码生成

二维码使用UUID去除连字符作为唯一编码：

```python
import uuid

def generate_code():
    return str(uuid.uuid4()).replace('-', '')

code = generate_code()  # 例如：a1b2c3d4e5f6789012345678901234ab
```

### 3. IP地址记录

系统自动记录扫码用户的IP地址，支持代理环境：

- 优先读取 `X-Forwarded-For` 头
- 其次读取 `X-Real-IP` 头
- 最后使用客户端直连IP

### 4. 防重复扫码

- 每个二维码只能扫码一次
- 重复扫码返回已使用提示
- 记录首次扫码的时间和IP

## 部署建议

### 1. 数据库优化

```sql
-- 添加索引优化查询性能
CREATE INDEX idx_lottery_code_prize_level ON lottery_code(prize_level);
CREATE INDEX idx_lottery_code_is_used ON lottery_code(is_used);
CREATE INDEX idx_lottery_code_scan_time ON lottery_code(scan_time);
CREATE INDEX idx_lottery_code_create_at ON lottery_code(create_at);
```

### 2. 缓存策略

建议使用Redis缓存热点数据：

- 缓存未使用的二维码列表
- 缓存统计数据（定时更新）
- 缓存奖品配置信息

### 3. 安全考虑

- 限制扫码频率（防刷）
- 验证二维码来源
- 记录异常扫码行为
- 定期备份中奖数据

## 监控指标

建议监控以下关键指标：

1. **参与率**：扫码人数 / 二维码总数
2. **中奖率**：中奖人数 / 扫码人数  
3. **热点时段**：扫码时间分布
4. **地域分布**：基于IP的地域统计
5. **异常检测**：重复扫码、批量扫码等

## 故障排除

### 常见问题

1. **二维码编码格式错误**
   - 确保编码为32位十六进制字符串
   - 检查是否包含非法字符

2. **重复扫码提示**
   - 检查数据库中的使用状态
   - 确认是否为同一用户重复操作

3. **统计数据不准确**
   - 检查数据库连接状态
   - 验证统计SQL查询逻辑

4. **性能问题**
   - 检查数据库索引
   - 监控并发扫码量
   - 考虑增加缓存层

## 扩展开发

### 1. 自定义奖品类型

```python
# 扩展奖品配置
PRIZE_CONFIG = {
    0: {"name": "谢谢参与", "type": "none"},
    1: {"name": "iPhone 15", "type": "physical", "value": 5999},
    2: {"name": "100元红包", "type": "cash", "value": 100},
    3: {"name": "优惠券", "type": "coupon", "value": 50}
}
```

### 2. 中奖通知

```python
# 集成消息推送
async def notify_winner(user_info, prize_info):
    # 发送短信通知
    # 发送邮件通知  
    # 推送APP消息
    pass
```

### 3. 数据导出

```python
# 导出中奖数据
async def export_winners():
    # 查询中奖记录
    # 生成Excel报表
    # 发送给管理员
    pass
```

这个抽奖系统提供了完整的功能和扩展性，可以满足各种抽奖活动的需求！
